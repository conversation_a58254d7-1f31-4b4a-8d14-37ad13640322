const fs = require('fs');
const path = require('path');
const BarcodeDecoder = require('./services/barcodeDecoder');

/**
 * 测试修改后的BarcodeDecoder
 */
async function testBarcodeDecoder() {
  console.log('🔍 测试修改后的BarcodeDecoder...\n');

  try {
    // 1. 创建BarcodeDecoder实例
    console.log('📦 创建BarcodeDecoder实例...');
    const decoder = new BarcodeDecoder();
    console.log('✅ BarcodeDecoder创建成功');

    // 2. 从调试文件中读取base64数据
    console.log('\n📄 读取条码base64数据...');
    const debugFile = path.join(__dirname, 'debug-textin-raw.json');
    
    if (!fs.existsSync(debugFile)) {
      throw new Error('调试文件不存在，请先运行 debug-barcode.js');
    }

    const debugData = JSON.parse(fs.readFileSync(debugFile, 'utf8'));
    const imageElements = debugData.result.pages[0].content.filter(item => item.type === 'image');
    
    if (imageElements.length === 0) {
      throw new Error('没有找到图像元素');
    }

    const barcodeElement = imageElements.find(img => img.sub_type === 'barcode');
    if (!barcodeElement) {
      throw new Error('没有找到条码元素');
    }

    const base64Data = barcodeElement.data?.base64 || barcodeElement.base64str;
    if (!base64Data) {
      throw new Error('没有找到base64数据');
    }

    console.log(`✅ 找到条码base64数据，长度: ${base64Data.length}`);

    // 3. 测试条码解析
    console.log('\n🔍 测试条码解析...');
    
    try {
      const result = await decoder.decode(base64Data, 'barcode');
      console.log('🎉 条码解析成功!');
      console.log(`📝 条码内容: "${result}"`);
      
      // 验证条码类型
      const detectedType = decoder.detectBarcodeType(result);
      console.log(`📊 检测到的条码类型: ${detectedType}`);
      
      return result;
    } catch (decodeError) {
      console.error('❌ 条码解析失败:', decodeError.message);
      console.error('错误详情:', decodeError);
      
      // 4. 如果解析失败，尝试分析原因
      console.log('\n🔧 分析失败原因...');
      await analyzeBarcodeFailure(base64Data, decoder);
    }

  } catch (error) {
    console.error('💥 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

/**
 * 分析条码解析失败的原因
 */
async function analyzeBarcodeFailure(base64Data, decoder) {
  try {
    console.log('📊 分析条码图像...');
    
    // 清理base64数据
    let cleanBase64 = base64Data;
    if (cleanBase64.startsWith('data:')) {
      const commaIndex = cleanBase64.indexOf(',');
      if (commaIndex !== -1) {
        cleanBase64 = cleanBase64.substring(commaIndex + 1);
      }
    }
    cleanBase64 = cleanBase64.replace(/\s/g, '');

    const Jimp = require('jimp');
    const imageBuffer = Buffer.from(cleanBase64, 'base64');
    const image = await Jimp.read(imageBuffer);
    
    console.log(`图像尺寸: ${image.getWidth()} x ${image.getHeight()}`);
    
    // 分析图像特征
    const stats = analyzeImageStats(image);
    console.log('图像统计:', stats);
    
    // 保存图像用于手动检查
    const outputPath = path.join(__dirname, 'debug-barcode-analysis.png');
    await image.writeAsync(outputPath);
    console.log(`✅ 条码图像已保存到: ${outputPath}`);
    
    // 尝试不同的预处理方法
    console.log('\n🔧 尝试不同的预处理方法...');
    
    const preprocessMethods = [
      { name: '灰度化', fn: (img) => img.clone().greyscale() },
      { name: '增强对比度', fn: (img) => img.clone().greyscale().contrast(0.8) },
      { name: '调整亮度', fn: (img) => img.clone().greyscale().brightness(0.2) },
      { name: '反色', fn: (img) => img.clone().greyscale().invert() },
      { name: '放大2倍', fn: (img) => img.clone().greyscale().scale(2, Jimp.RESIZE_NEAREST_NEIGHBOR) }
    ];
    
    for (let i = 0; i < preprocessMethods.length; i++) {
      const method = preprocessMethods[i];
      try {
        console.log(`尝试方法 ${i + 1}: ${method.name}...`);
        const processedImage = method.fn(image);
        
        // 保存预处理后的图像
        const processedPath = path.join(__dirname, `debug-barcode-${i + 1}-${method.name}.png`);
        await processedImage.writeAsync(processedPath);
        
        // 尝试解析
        const processedBase64 = await imageToBase64(processedImage);
        const result = await decoder.decode(processedBase64, 'barcode');
        
        console.log(`🎉 方法 ${i + 1} 成功! 结果: "${result}"`);
        return result;
        
      } catch (methodError) {
        console.log(`❌ 方法 ${i + 1} 失败: ${methodError.message}`);
      }
    }
    
    console.log('⚠️  所有预处理方法都失败了');
    
  } catch (error) {
    console.error('分析过程中发生错误:', error.message);
  }
}

/**
 * 分析图像统计信息
 */
function analyzeImageStats(image) {
  const width = image.getWidth();
  const height = image.getHeight();
  let totalBrightness = 0;
  let pixelCount = 0;
  
  image.scan(0, 0, width, height, (x, y, idx) => {
    const pixel = Jimp.intToRGBA(image.getPixelColor(x, y));
    const brightness = 0.299 * pixel.r + 0.587 * pixel.g + 0.114 * pixel.b;
    totalBrightness += brightness;
    pixelCount++;
  });
  
  const avgBrightness = totalBrightness / pixelCount;
  
  return {
    width,
    height,
    pixelCount,
    averageBrightness: Math.round(avgBrightness),
    isLowContrast: avgBrightness > 200 || avgBrightness < 50
  };
}

/**
 * 将Jimp图像转换为base64
 */
async function imageToBase64(image) {
  return new Promise((resolve, reject) => {
    image.getBase64(Jimp.MIME_PNG, (err, base64) => {
      if (err) reject(err);
      else resolve(base64);
    });
  });
}

// 执行测试
testBarcodeDecoder().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
