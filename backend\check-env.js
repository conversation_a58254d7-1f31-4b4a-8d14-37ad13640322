/**
 * 检查环境变量配置
 */

// 加载环境变量
require('dotenv').config();

console.log('🔍 检查环境变量配置...\n');

// 检查必要的环境变量
const requiredEnvVars = [
  'PORT',
  'TEXTIN_APP_ID', 
  'TEXTIN_SECRET_CODE'
];

const optionalEnvVars = [
  'NODE_ENV',
  'MAX_FILE_SIZE',
  'UPLOAD_DIR',
  'LOG_LEVEL'
];

console.log('📋 必需的环境变量:');
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // 对敏感信息进行脱敏显示
    if (varName.includes('SECRET') || varName.includes('KEY')) {
      const maskedValue = value.length > 4 ? 
        value.substring(0, 4) + '*'.repeat(value.length - 4) : 
        '*'.repeat(value.length);
      console.log(`✅ ${varName}: ${maskedValue}`);
    } else {
      console.log(`✅ ${varName}: ${value}`);
    }
  } else {
    console.log(`❌ ${varName}: 未设置`);
  }
});

console.log('\n📋 可选的环境变量:');
optionalEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value}`);
  } else {
    console.log(`⚪ ${varName}: 未设置 (使用默认值)`);
  }
});

// 检查TextIn API配置状态
console.log('\n🔑 TextIn API配置状态:');
const hasAppId = !!process.env.TEXTIN_APP_ID;
const hasSecretCode = !!process.env.TEXTIN_SECRET_CODE;

if (hasAppId && hasSecretCode) {
  console.log('✅ TextIn API密钥已配置，可以使用真实API');
  console.log('🚀 建议运行: node test-real-images.js 测试真实识别');
} else {
  console.log('⚠️  TextIn API密钥未完整配置，将使用模拟模式');
  console.log('💡 要使用真实API，请在.env文件中设置:');
  console.log('   TEXTIN_APP_ID=your_app_id');
  console.log('   TEXTIN_SECRET_CODE=your_secret_code');
}

// 检查服务器状态
console.log('\n🌐 服务器状态检查:');
const port = process.env.PORT || 3001;

const axios = require('axios');

async function checkServerStatus() {
  try {
    const response = await axios.get(`http://localhost:${port}/health`, {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log(`✅ 服务器运行正常 (端口 ${port})`);
      console.log(`🔗 健康检查: http://localhost:${port}/health`);
      console.log(`📚 API文档: http://localhost:${port}/api/docs`);
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log(`❌ 服务器未运行 (端口 ${port})`);
      console.log('💡 请先启动服务器: npm start');
    } else {
      console.log(`⚠️  服务器状态检查失败: ${error.message}`);
    }
  }
}

checkServerStatus().then(() => {
  console.log('\n✅ 环境检查完成!');
}).catch(error => {
  console.error('\n❌ 检查过程中发生错误:', error.message);
});
