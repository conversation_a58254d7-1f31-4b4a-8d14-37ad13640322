{"success": true, "data": {"imageInfo": {"width": 480, "height": 360, "format": "png"}, "elements": [{"elementType": "1", "x": 140, "y": 3, "width": 189, "height": 40, "content": "火锅肥牛卷", "charWidth": 35.4, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 25, "y": 55, "width": 231, "height": 40, "content": "保质期：30天", "charWidth": 26.57, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 25, "y": 105, "width": 419, "height": 40, "content": "保存方式：零下18℃冷冻", "charWidth": 29.17, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 27, "y": 152, "width": 170, "height": 40, "content": "生产日期：", "charWidth": 28.2, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 16, "y": 209, "width": 448, "height": 86, "content": "", "charWidth": 12, "bold": false, "italic": false, "angle": 0}, {"elementType": "2", "x": 16, "y": 209, "width": 448, "height": 86, "content": "18888888888", "barcodeType": "CODE_128", "angle": 0}, {"elementType": "1", "x": 113, "y": 304, "width": 258, "height": 40, "content": "18888888888", "charWidth": 21.09, "bold": false, "italic": false, "angle": 0}]}, "message": "识别成功"}