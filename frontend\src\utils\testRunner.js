/**
 * 简单的测试运行器
 * 用于在浏览器环境中运行单元测试
 */

import { WordSpaceCalculator } from './wordSpaceCalculator';
import { calculateCharBounds } from './charPosAnalyzer';

class TestRunner {
  constructor() {
    this.tests = [];
    this.results = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  describe(description, testSuite) {
    console.log(`\n=== ${description} ===`);
    testSuite();
  }

  test(description, testFunction) {
    this.results.total++;
    try {
      testFunction();
      this.results.passed++;
      console.log(`✅ ${description}`);
      this.results.details.push({ description, status: 'passed', error: null });
    } catch (error) {
      this.results.failed++;
      console.error(`❌ ${description}`);
      console.error(`   错误: ${error.message}`);
      this.results.details.push({ description, status: 'failed', error: error.message });
    }
  }

  expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`期望 ${expected}, 但得到 ${actual}`);
        }
      },
      toBeCloseTo: (expected, precision = 2) => {
        const diff = Math.abs(actual - expected);
        const tolerance = Math.pow(10, -precision);
        if (diff > tolerance) {
          throw new Error(`期望 ${expected} (精度 ${precision}), 但得到 ${actual}`);
        }
      },
      toBeGreaterThanOrEqual: (expected) => {
        if (actual < expected) {
          throw new Error(`期望 >= ${expected}, 但得到 ${actual}`);
        }
      },
      toBeDefined: () => {
        if (actual === undefined) {
          throw new Error('期望已定义, 但得到 undefined');
        }
      },
      toHaveLength: (expected) => {
        if (!actual || actual.length !== expected) {
          throw new Error(`期望长度 ${expected}, 但得到 ${actual ? actual.length : 'undefined'}`);
        }
      },
      toContain: (expected) => {
        if (!actual || !actual.includes(expected)) {
          throw new Error(`期望包含 "${expected}", 但在 "${actual}" 中未找到`);
        }
      }
    };
  }

  beforeEach(setupFunction) {
    this.setupFunction = setupFunction;
  }

  runSetup() {
    if (this.setupFunction) {
      this.setupFunction();
    }
  }

  getSummary() {
    return this.results;
  }
}

/**
 * 运行所有字间距计算器测试
 */
export function runWordSpaceCalculatorTests() {
  const runner = new TestRunner();
  let calculator;

  // 设置
  runner.beforeEach(() => {
    calculator = new WordSpaceCalculator();
    calculator.setDebugMode(false);
  });

  console.log('🧪 开始运行 WordSpaceCalculator 单元测试...\n');

  // 基本功能测试
  runner.describe('基本功能测试', () => {
    runner.runSetup();

    runner.test('应该正确计算正常字符间距', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120],
        [130, 100, 150, 100, 150, 120, 130, 120]
      ];
      const text = '测试';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      runner.expect(result.wordSpacePx).toBe(10);
      runner.expect(result.wordSpaceMm).toBe(1);
      runner.expect(result.method).toBe('non_overlapping_average');
      runner.expect(result.details.charCount).toBe(2);
      runner.expect(result.details.validGaps).toHaveLength(1);
    });

    runner.test('应该处理多个字符的平均间距', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120],
        [130, 100, 150, 100, 150, 120, 130, 120],
        [165, 100, 185, 100, 185, 120, 165, 120]
      ];
      const text = '测试字';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      runner.expect(result.wordSpacePx).toBe(12.5);
      runner.expect(result.wordSpaceMm).toBe(1.25);
      runner.expect(result.details.validGaps).toHaveLength(2);
    });
  });

  // 边界情况测试
  runner.describe('边界情况测试', () => {
    runner.runSetup();

    runner.test('应该处理单个字符（无间距）', () => {
      const charPos = [[100, 100, 120, 100, 120, 120, 100, 120]];
      const text = '单';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      runner.expect(result.wordSpacePx).toBe(0);
      runner.expect(result.wordSpaceMm).toBe(0);
      runner.expect(result.method).toBe('no_chars');
      runner.expect(result.details.gaps).toHaveLength(0);
    });

    runner.test('应该处理字符重叠情况', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120],
        [115, 100, 135, 100, 135, 120, 115, 120]
      ];
      const text = '重叠';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      runner.expect(result.wordSpacePx).toBe(0);
      runner.expect(result.wordSpaceMm).toBe(0);
      runner.expect(result.method).toBe('all_overlapping');
    });

    runner.test('应该处理空数据', () => {
      const result = calculator.calculateWordSpace([], '', 0.1);

      runner.expect(result.wordSpacePx).toBe(0);
      runner.expect(result.wordSpaceMm).toBe(0);
      runner.expect(result.method).toBe('no_chars');
      runner.expect(result.details.charCount).toBe(0);
    });
  });

  // 单位转换测试
  runner.describe('单位转换测试', () => {
    runner.runSetup();

    runner.test('应该正确转换不同的缩放比例', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120],
        [130, 100, 150, 100, 150, 120, 130, 120]
      ];
      const text = '转换';

      const scales = [0.05, 0.1, 0.2];
      const expectedPixels = 10;

      scales.forEach(scale => {
        const result = calculator.calculateWordSpace(charPos, text, scale);
        runner.expect(result.wordSpacePx).toBe(expectedPixels);
        runner.expect(result.wordSpaceMm).toBeCloseTo(expectedPixels * scale, 3);
      });
    });
  });

  // 验证功能测试
  runner.describe('验证功能测试', () => {
    runner.runSetup();

    runner.test('应该验证合理的字间距', () => {
      const validation = calculator.validateWordSpace(5, 20);

      runner.expect(validation.isReasonable).toBe(true);
      runner.expect(validation.ratio).toBe(0.25);
      runner.expect(validation.quality).toBe('good');
    });

    runner.test('应该检测过大的字间距', () => {
      const validation = calculator.validateWordSpace(15, 20);

      runner.expect(validation.isReasonable).toBe(false);
      runner.expect(validation.quality).toBe('too_large');
      runner.expect(validation.recommendation).toContain('过大');
    });
  });

  // 字符边界框测试
  runner.describe('字符边界框计算测试', () => {
    runner.test('应该正确计算字符边界框', () => {
      const charPos = [100, 50, 120, 50, 120, 70, 100, 70];
      const bounds = calculateCharBounds(charPos);

      runner.expect(bounds.x).toBe(100);
      runner.expect(bounds.y).toBe(50);
      runner.expect(bounds.width).toBe(20);
      runner.expect(bounds.height).toBe(20);
    });

    runner.test('应该处理无效的char_pos数据', () => {
      const bounds = calculateCharBounds([]);
      
      runner.expect(bounds.x).toBe(0);
      runner.expect(bounds.y).toBe(0);
      runner.expect(bounds.width).toBe(0);
      runner.expect(bounds.height).toBe(0);
    });
  });

  // 输出测试结果
  const summary = runner.getSummary();
  console.log('\n📊 测试结果汇总:');
  console.log(`总计: ${summary.total} 个测试`);
  console.log(`通过: ${summary.passed} 个`);
  console.log(`失败: ${summary.failed} 个`);
  console.log(`成功率: ${((summary.passed / summary.total) * 100).toFixed(1)}%`);

  if (summary.failed > 0) {
    console.log('\n❌ 失败的测试:');
    summary.details
      .filter(detail => detail.status === 'failed')
      .forEach(detail => {
        console.log(`  - ${detail.description}: ${detail.error}`);
      });
  }

  return summary;
}

// 在浏览器环境中暴露测试函数
if (typeof window !== 'undefined') {
  window.runWordSpaceCalculatorTests = runWordSpaceCalculatorTests;
  console.log('单元测试已加载，可以在控制台中调用: window.runWordSpaceCalculatorTests()');
}
