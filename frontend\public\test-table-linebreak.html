<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格换行测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .test-table td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        
        .problem-demo {
            background-color: #fff2f0;
            border-color: #ffccc7;
        }
        
        .solution-demo {
            background-color: #f6ffed;
            border-color: #b7eb8f;
        }
        
        .code-block {
            background-color: #f8f8f8;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .br-visible {
            color: #ff4d4f;
            font-weight: bold;
        }
        
        .react-demo {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background-color: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>表格单元格换行问题修复测试</h1>
        
        <div class="test-section problem-demo">
            <div class="test-title">❌ 问题演示：HTML标签未被解析</div>
            <p>当单元格内容包含 <code>&lt;br&gt;</code> 标签时，在React中直接显示会被当作普通文本：</p>
            
            <table class="test-table">
                <tr>
                    <td style="width: 150px;">原始数据</td>
                    <td>脂肪&lt;br&gt;碳水化合物</td>
                </tr>
                <tr>
                    <td>React直接渲染</td>
                    <td>脂肪&lt;br&gt;碳水化合物</td>
                </tr>
            </table>
            
            <div class="code-block">
// 问题代码
&lt;Text&gt;{value}&lt;/Text&gt;
// 结果：脂肪&lt;br&gt;碳水化合物 (标签被当作文本显示)
            </div>
        </div>
        
        <div class="test-section solution-demo">
            <div class="test-title">✅ 解决方案：解析HTML标签为React元素</div>
            <p>通过解析 <code>&lt;br&gt;</code> 标签并转换为React的 <code>&lt;br /&gt;</code> 元素：</p>
            
            <table class="test-table">
                <tr>
                    <td style="width: 150px;">原始数据</td>
                    <td>脂肪&lt;br&gt;碳水化合物</td>
                </tr>
                <tr>
                    <td>修复后渲染</td>
                    <td>脂肪<br>碳水化合物</td>
                </tr>
            </table>
            
            <div class="code-block">
// 解决方案代码
const renderTextWithLineBreaks = (text) => {
  if (!text) return null;
  
  // 处理 &lt;br&gt; 标签，将其转换为换行
  const parts = text.split(/&lt;br\s*\/?&gt;/gi);
  
  return parts.map((part, index) => (
    &lt;React.Fragment key={index}&gt;
      {part}
      {index &lt; parts.length - 1 && &lt;br /&gt;}
    &lt;/React.Fragment&gt;
  ));
};

// 使用
&lt;Text&gt;{renderTextWithLineBreaks(value)}&lt;/Text&gt;
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🧪 更多测试用例</div>
            
            <table class="test-table">
                <thead>
                    <tr style="background-color: #f0f0f0;">
                        <th style="width: 200px;">测试内容</th>
                        <th style="width: 200px;">原始数据</th>
                        <th>渲染效果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>单个换行</td>
                        <td>脂肪&lt;br&gt;碳水化合物</td>
                        <td>脂肪<br>碳水化合物</td>
                    </tr>
                    <tr>
                        <td>多个换行</td>
                        <td>蛋白质&lt;br&gt;脂肪&lt;br&gt;碳水化合物</td>
                        <td>蛋白质<br>脂肪<br>碳水化合物</td>
                    </tr>
                    <tr>
                        <td>自闭合标签</td>
                        <td>维生素A&lt;br/&gt;维生素C</td>
                        <td>维生素A<br/>维生素C</td>
                    </tr>
                    <tr>
                        <td>带空格的标签</td>
                        <td>钙&lt;br &gt;铁</td>
                        <td>钙<br />铁</td>
                    </tr>
                    <tr>
                        <td>无换行标签</td>
                        <td>普通文本内容</td>
                        <td>普通文本内容</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <div class="test-title">📋 修复说明</div>
            <ul>
                <li><strong>问题原因</strong>：React中字符串内的HTML标签会被转义，不会被解析为HTML元素</li>
                <li><strong>解决方案</strong>：创建 <code>renderTextWithLineBreaks</code> 函数，将 <code>&lt;br&gt;</code> 标签转换为React的 <code>&lt;br /&gt;</code> 元素</li>
                <li><strong>支持格式</strong>：<code>&lt;br&gt;</code>、<code>&lt;br/&gt;</code>、<code>&lt;br /&gt;</code>、<code>&lt;BR&gt;</code> 等各种格式</li>
                <li><strong>兼容性</strong>：同时支持普通模式和拉伸模式的文本渲染</li>
                <li><strong>安全性</strong>：只处理 <code>&lt;br&gt;</code> 标签，不会引入XSS风险</li>
            </ul>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 实现细节</div>
            <p>修改了 <code>EditableText.jsx</code> 组件：</p>
            <ol>
                <li>添加 <code>renderTextWithLineBreaks</code> 函数处理普通文本</li>
                <li>添加 <code>renderStretchText</code> 函数处理拉伸模式文本</li>
                <li>使用正则表达式 <code>/&lt;br\s*\/?&gt;/gi</code> 匹配各种 <code>&lt;br&gt;</code> 格式</li>
                <li>将匹配的标签替换为React的 <code>&lt;br /&gt;</code> 元素</li>
            </ol>
        </div>
        
        <div class="test-section">
            <div class="test-title">🎯 使用场景</div>
            <p>这个修复主要解决以下场景的问题：</p>
            <ul>
                <li>营养成分表格中的多行文本（如：脂肪&lt;br&gt;碳水化合物）</li>
                <li>产品规格表格中的多行描述</li>
                <li>任何需要在表格单元格中显示多行文本的场景</li>
                <li>从后端API返回的包含HTML换行标签的文本内容</li>
            </ul>
        </div>
    </div>
</body>
</html>
