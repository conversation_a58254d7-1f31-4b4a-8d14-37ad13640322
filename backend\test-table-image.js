const fs = require('fs');
const path = require('path');

// 加载环境变量
require('dotenv').config();

const TextInService = require('./services/textinService');
const DataConverter = require('./services/dataConverter');

/**
 * 测试包含表格的图片识别
 */
async function testTableImage() {
  console.log('🔍 测试包含表格的图片识别...\n');

  // 查找包含表格的测试图片
  const dataDir = path.join(__dirname, 'data');
  const imageFiles = fs.readdirSync(dataDir).filter(file => 
    file.toLowerCase().endsWith('.png') || file.toLowerCase().endsWith('.jpg')
  );

  console.log('📁 可用的测试图片:');
  imageFiles.forEach((file, index) => {
    console.log(`${index + 1}. ${file}`);
  });

  // 选择一个可能包含表格的图片进行测试
  const testImage = imageFiles.find(file => 
    file.includes('营养') || file.includes('表格') || file.includes('成分')
  ) || imageFiles[0];

  const imagePath = path.join(dataDir, testImage);
  console.log(`\n📷 测试图片: ${testImage}`);
  
  try {
    // 1. 直接调用TextIn API
    console.log('\n🔧 步骤1: 调用TextIn API...');
    const textinService = new TextInService();
    const textinResult = await textinService.recognizeFile(imagePath);
    
    // 保存原始响应
    const rawOutputPath = path.join(__dirname, 'debug-table-raw.json');
    fs.writeFileSync(rawOutputPath, JSON.stringify(textinResult, null, 2));
    console.log(`💾 原始TextIn响应已保存到: ${rawOutputPath}`);
    
    // 2. 分析TextIn响应中的表格元素
    console.log('\n🔍 步骤2: 分析TextIn响应中的表格元素...');
    
    // 检查structured数组中的表格
    const structured = textinResult.result?.structured || [];
    const tables = structured.filter(item => item.type === 'table');
    
    console.log(`structured数组中找到 ${tables.length} 个表格:`);
    tables.forEach((table, index) => {
      console.log(`\n表格 ${index + 1}:`);
      console.log(`- 类型: ${table.type}`);
      console.log(`- 行数: ${table.rows || '未知'}`);
      console.log(`- 列数: ${table.cols || '未知'}`);
      console.log(`- 单元格数量: ${table.cells?.length || 0}`);
      if (table.cells && table.cells.length > 0) {
        console.log(`- 前3个单元格内容:`);
        table.cells.slice(0, 3).forEach((cell, cellIndex) => {
          console.log(`  ${cellIndex + 1}. [${cell.row},${cell.col}]: "${cell.text || cell.content || '空'}"`);
        });
      }
    });
    
    // 检查detail数组中的表格
    const detail = textinResult.result?.detail || [];
    const detailTables = detail.filter(item => item.type === 'table');
    
    console.log(`\ndetail数组中找到 ${detailTables.length} 个表格:`);
    detailTables.forEach((table, index) => {
      console.log(`\n表格 ${index + 1}:`);
      console.log(`- 类型: ${table.type}`);
      console.log(`- 子类型: ${table.sub_type || '无'}`);
      console.log(`- paragraph_id: ${table.paragraph_id}`);
    });
    
    // 3. 转换为APP格式
    console.log('\n🔄 步骤3: 转换为APP格式...');
    const dataConverter = new DataConverter();
    const imageInfo = { width: 800, height: 600, format: 'png' };
    const elements = await dataConverter.convertToAppFormat(textinResult, imageInfo);
    
    console.log(`✅ 转换完成，共生成 ${elements.length} 个元素\n`);
    
    // 4. 分析转换结果
    console.log('📊 转换结果分析:');
    elements.forEach((element, index) => {
      console.log(`\n元素 ${index + 1}:`);
      console.log(`- 类型: ${getElementTypeName(element.elementType)}`);
      
      if (element.elementType === '10') { // 表格元素
        console.log(`- 行数: ${element.rows}`);
        console.log(`- 列数: ${element.cols}`);
        console.log(`- 单元格数量: ${element.cells?.length || 0}`);
        console.log(`- 列宽: [${element.columnWidths?.join(', ') || '无'}]`);
        console.log(`- 行高: [${element.rowHeights?.join(', ') || '无'}]`);
        
        if (element.cells && element.cells.length > 0) {
          console.log(`- 前5个单元格:`);
          element.cells.slice(0, 5).forEach((cell, cellIndex) => {
            console.log(`  ${cellIndex + 1}. [${cell.row},${cell.col}]: "${cell.content}" (粗体: ${cell.bold ? '是' : '否'})`);
          });
        }
      } else if (element.elementType === '1') { // 文本元素
        console.log(`- 内容: "${element.content}"`);
        console.log(`- 粗体: ${element.bold ? '是' : '否'}`);
        console.log(`- 字符宽度: ${element.charWidth}px`);
      } else {
        console.log(`- 内容: "${element.content}"`);
      }
    });

    // 5. 检查表格识别结果
    console.log('\n🎯 表格识别结果:');
    const tableElements = elements.filter(el => el.elementType === '10');
    if (tableElements.length > 0) {
      console.log(`✅ 发现 ${tableElements.length} 个表格元素:`);
      tableElements.forEach((element, index) => {
        console.log(`${index + 1}. ${element.rows}行 x ${element.cols}列表格，包含 ${element.cells?.length || 0} 个单元格`);
      });
    } else {
      console.log('⚠️  没有发现表格元素');
      console.log('💡 可能的原因:');
      console.log('1. 图片中没有表格');
      console.log('2. TextIn API没有识别出表格');
      console.log('3. 表格转换逻辑有问题');
      console.log('4. 表格被识别为其他类型的元素');
    }

    // 保存转换结果
    const resultOutputPath = path.join(__dirname, 'debug-table-result.json');
    fs.writeFileSync(resultOutputPath, JSON.stringify({
      success: true,
      data: {
        imageInfo: imageInfo,
        elements: elements
      }
    }, null, 2));
    console.log(`\n💾 转换结果已保存到: ${resultOutputPath}`);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

/**
 * 获取元素类型名称
 */
function getElementTypeName(elementType) {
  const typeNames = {
    '1': '文本',
    '2': '一维码',
    '7': '二维码',
    '10': '表格'
  };
  return typeNames[elementType] || `未知(${elementType})`;
}

// 执行测试
testTableImage().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
