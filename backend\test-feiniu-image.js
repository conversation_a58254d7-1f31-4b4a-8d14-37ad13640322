const fs = require('fs');
const path = require('path');

// 加载环境变量
require('dotenv').config();

const TextInService = require('./services/textinService');
const DataConverter = require('./services/dataConverter');

/**
 * 测试肥牛卷图片的一维码识别
 */
async function testFeiniuImage() {
  console.log('🔍 测试肥牛卷图片的一维码识别...\n');

  const imagePath = path.join(__dirname, 'data', '肥牛卷（40_30）.png');
  
  if (!fs.existsSync(imagePath)) {
    console.error('❌ 测试图片不存在:', imagePath);
    return;
  }

  console.log('📷 测试图片:', imagePath);
  
  try {
    // 1. 直接调用TextIn API
    console.log('\n🔧 步骤1: 调用TextIn API...');
    const textinService = new TextInService();
    const textinResult = await textinService.recognizeFile(imagePath);
    
    // 保存原始响应
    const rawOutputPath = path.join(__dirname, 'debug-feiniu-raw.json');
    fs.writeFileSync(rawOutputPath, JSON.stringify(textinResult, null, 2));
    console.log(`💾 原始TextIn响应已保存到: ${rawOutputPath}`);
    
    // 2. 分析TextIn响应中的图像元素
    console.log('\n🔍 步骤2: 分析TextIn响应中的图像元素...');
    const pages = textinResult.result?.pages || [];
    if (pages.length > 0) {
      const content = pages[0].content || [];
      const images = content.filter(item => item.type === 'image');
      
      console.log(`找到 ${images.length} 个图像元素:`);
      images.forEach((image, index) => {
        console.log(`\n图像 ${index + 1}:`);
        console.log(`- 类型: ${image.type}`);
        console.log(`- 子类型: ${image.sub_type || '无'}`);
        console.log(`- 文本: ${image.text || '无'}`);
        console.log(`- 位置: [${image.pos?.join(', ') || '无'}]`);
        console.log(`- 是否有base64数据: ${image.base64 ? '是' : '否'}`);
        if (image.base64) {
          console.log(`- Base64数据长度: ${image.base64.length} 字符`);
          console.log(`- Base64前缀: ${image.base64.substring(0, 50)}...`);
        }
      });
    }
    
    // 3. 分析detail数组
    console.log('\n🔍 步骤3: 分析detail数组...');
    const detail = textinResult.result?.detail || [];
    console.log(`detail数组包含 ${detail.length} 个项目:`);
    detail.forEach((item, index) => {
      console.log(`\n项目 ${index + 1}:`);
      console.log(`- 类型: ${item.type}`);
      console.log(`- 子类型: ${item.sub_type || '无'}`);
      console.log(`- 文本: "${item.text || '无'}"`);
      console.log(`- paragraph_id: ${item.paragraph_id}`);
    });
    
    // 4. 转换为APP格式
    console.log('\n🔄 步骤4: 转换为APP格式...');
    const dataConverter = new DataConverter();
    const imageInfo = { width: 480, height: 360, format: 'png' };
    const elements = await dataConverter.convertToAppFormat(textinResult, imageInfo);
    
    console.log(`✅ 转换完成，共生成 ${elements.length} 个元素\n`);
    
    // 5. 分析转换结果
    console.log('📊 转换结果分析:');
    elements.forEach((element, index) => {
      console.log(`\n元素 ${index + 1}:`);
      console.log(`- 类型: ${getElementTypeName(element.elementType)}`);
      console.log(`- 内容: "${element.content}"`);
      
      if (element.elementType === '1') { // 文本元素
        console.log(`- 粗体: ${element.bold ? '是' : '否'}`);
        console.log(`- 字符宽度: ${element.charWidth}px`);
      } else if (element.elementType === '2') { // 一维码
        console.log(`- 条码类型: ${element.barcodeType || '未知'}`);
        console.log(`- 位置: (${element.x}, ${element.y})`);
        console.log(`- 尺寸: ${element.width} x ${element.height}`);
      }
    });

    // 6. 检查一维码识别结果
    console.log('\n🎯 一维码识别结果:');
    const barcodeElements = elements.filter(el => el.elementType === '2');
    if (barcodeElements.length > 0) {
      console.log(`✅ 发现 ${barcodeElements.length} 个一维码元素:`);
      barcodeElements.forEach((element, index) => {
        console.log(`${index + 1}. "${element.content}" (${element.barcodeType || '未知类型'})`);
      });
    } else {
      console.log('⚠️  没有发现一维码元素');
      console.log('💡 可能的原因:');
      console.log('1. TextIn API没有识别出条码');
      console.log('2. 条码图像质量不够清晰');
      console.log('3. 数据转换逻辑有问题');
      console.log('4. 条码被识别为其他类型的元素');
    }

    // 保存转换结果
    const resultOutputPath = path.join(__dirname, 'debug-feiniu-result.json');
    fs.writeFileSync(resultOutputPath, JSON.stringify({
      success: true,
      data: {
        imageInfo: imageInfo,
        elements: elements
      }
    }, null, 2));
    console.log(`\n💾 转换结果已保存到: ${resultOutputPath}`);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

/**
 * 获取元素类型名称
 */
function getElementTypeName(elementType) {
  const typeNames = {
    '1': '文本',
    '2': '一维码',
    '7': '二维码',
    '10': '表格'
  };
  return typeNames[elementType] || `未知(${elementType})`;
}

// 执行测试
testFeiniuImage().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
