/**
 * 简化的ZXing测试
 */
async function testZXingSimple() {
  console.log('🔍 简化的ZXing测试...\n');

  try {
    // 1. 测试不同的ZXing导入方式
    console.log('📦 测试ZXing导入...');
    
    let ZXing;
    try {
      ZXing = require('@zxing/library');
      console.log('✅ 方式1: require("@zxing/library") 成功');
      console.log('可用的导出:', Object.keys(ZXing));
    } catch (error) {
      console.error('❌ 方式1失败:', error.message);
    }

    // 2. 测试具体的类
    console.log('\n🔧 测试具体的ZXing类...');
    
    try {
      const { BrowserMultiFormatReader } = ZXing;
      if (BrowserMultiFormatReader) {
        console.log('✅ BrowserMultiFormatReader 可用');
        const reader = new BrowserMultiFormatReader();
        console.log('✅ BrowserMultiFormatReader 实例创建成功');
        console.log('可用方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(reader)));
      } else {
        console.log('❌ BrowserMultiFormatReader 不可用');
      }
    } catch (error) {
      console.error('❌ BrowserMultiFormatReader 测试失败:', error.message);
    }

    // 3. 测试其他可能的Reader类
    console.log('\n🔍 测试其他Reader类...');
    
    const possibleReaders = [
      'MultiFormatReader',
      'BrowserCodeReader', 
      'BrowserBarcodeReader',
      'BrowserQRCodeReader'
    ];

    for (const readerName of possibleReaders) {
      try {
        const ReaderClass = ZXing[readerName];
        if (ReaderClass) {
          console.log(`✅ ${readerName} 可用`);
          const reader = new ReaderClass();
          console.log(`✅ ${readerName} 实例创建成功`);
          console.log(`${readerName} 方法:`, Object.getOwnPropertyNames(Object.getPrototypeOf(reader)).slice(0, 5));
        } else {
          console.log(`❌ ${readerName} 不可用`);
        }
      } catch (error) {
        console.error(`❌ ${readerName} 测试失败:`, error.message);
      }
    }

    // 4. 尝试使用Canvas方式（如果支持的话）
    console.log('\n🖼️ 测试Canvas方式...');
    
    try {
      // 检查是否有Canvas相关的方法
      const canvasMethods = Object.keys(ZXing).filter(key => 
        key.toLowerCase().includes('canvas') || 
        key.toLowerCase().includes('image') ||
        key.toLowerCase().includes('decode')
      );
      
      if (canvasMethods.length > 0) {
        console.log('✅ 找到Canvas相关方法:', canvasMethods);
      } else {
        console.log('❌ 没有找到Canvas相关方法');
      }
    } catch (error) {
      console.error('❌ Canvas测试失败:', error.message);
    }

    // 5. 检查是否需要浏览器环境
    console.log('\n🌐 检查浏览器环境依赖...');
    
    if (typeof window === 'undefined') {
      console.log('⚠️  当前在Node.js环境中，@zxing/library可能需要浏览器环境');
      console.log('💡 建议: 考虑使用其他适合Node.js的条码解析库');
      
      // 推荐替代方案
      console.log('\n📚 推荐的Node.js条码解析库:');
      console.log('1. node-quirc (二维码)');
      console.log('2. jsqr (二维码)'); 
      console.log('3. quagga2 (一维码)');
      console.log('4. @ericblade/quagga2 (一维码)');
    } else {
      console.log('✅ 浏览器环境可用');
    }

  } catch (error) {
    console.error('💥 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 执行测试
testZXingSimple().then(() => {
  console.log('\n✅ 简化测试完成!');
}).catch(error => {
  console.error('\n❌ 简化测试失败:', error.message);
});
