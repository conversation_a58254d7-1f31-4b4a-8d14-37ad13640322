import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Slider, Input, Select, Row, Col, Alert, Divider, Table, Switch } from 'antd';
import { PlayCircleOutlined, ReloadOutlined, SettingOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { WordSpaceCalculator } from '../utils/wordSpaceCalculator';
import CharBoundingBoxOverlay from './CharBoundingBoxOverlay';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 字间距效果验证页面
 */
const WordSpaceValidationPage = () => {
  const [calculator] = useState(new WordSpaceCalculator());
  const [testResults, setTestResults] = useState([]);
  const [currentTest, setCurrentTest] = useState(null);
  const [showCharBounds, setShowCharBounds] = useState(false);
  const [settings, setSettings] = useState({
    canvasWidth: 800,
    canvasHeight: 600,
    canvasWidthMm: 40,
    canvasHeightMm: 30,
    scaleX: 0.1,
    fontSize: 12
  });

  // 预设测试案例
  const presetTests = [
    {
      name: '正常间距文本',
      text: '解冻标签',
      char_pos: [
        [203, 38, 244, 38, 244, 76, 203, 76],
        [287, 36, 328, 37, 328, 75, 287, 75],
        [370, 37, 413, 37, 413, 77, 370, 77],
        [454, 37, 497, 37, 497, 77, 454, 76]
      ],
      pos: [200, 33, 500, 33, 500, 81, 200, 81],
      expectedWordSpace: 1.5
    },
    {
      name: '紧密排列文本',
      text: '品名：',
      char_pos: [
        [43, 107, 75, 107, 74, 140, 43, 140],
        [78, 106, 112, 106, 111, 142, 79, 141],
        [121, 123, 129, 123, 128, 138, 121, 138]
      ],
      pos: [40, 105, 133, 105, 133, 144, 40, 144],
      expectedWordSpace: 0.5
    },
    {
      name: '宽间距文本',
      text: '测试',
      char_pos: [
        [100, 100, 130, 100, 130, 130, 100, 130],
        [160, 100, 190, 100, 190, 130, 160, 130]
      ],
      pos: [100, 100, 190, 100, 190, 130, 100, 130],
      expectedWordSpace: 3.0
    },
    {
      name: '重叠字符',
      text: '重叠',
      char_pos: [
        [100, 100, 130, 100, 130, 130, 100, 130],
        [125, 100, 155, 100, 155, 130, 125, 130]
      ],
      pos: [100, 100, 155, 100, 155, 130, 100, 130],
      expectedWordSpace: 0
    }
  ];

  const [customTest, setCustomTest] = useState({
    name: '自定义测试',
    text: '',
    char_pos: [],
    pos: [],
    expectedWordSpace: 0
  });

  // 运行测试
  const runTest = (testCase) => {
    const result = calculator.calculateWordSpace(
      testCase.char_pos,
      testCase.text,
      settings.scaleX
    );

    const testResult = {
      ...testCase,
      result,
      timestamp: new Date().toLocaleTimeString(),
      settings: { ...settings },
      accuracy: calculateAccuracy(result.wordSpaceMm, testCase.expectedWordSpace)
    };

    setCurrentTest(testResult);
    setTestResults(prev => [testResult, ...prev.slice(0, 9)]); // 保留最近10个结果
  };

  // 计算准确率
  const calculateAccuracy = (actual, expected) => {
    if (expected === 0) {
      return actual === 0 ? 100 : 0;
    }
    const diff = Math.abs(actual - expected);
    const accuracy = Math.max(0, 100 - (diff / expected) * 100);
    return Math.round(accuracy * 100) / 100;
  };

  // 运行所有预设测试
  const runAllTests = () => {
    presetTests.forEach((test, index) => {
      setTimeout(() => runTest(test), index * 100);
    });
  };

  // 渲染文本预览
  const renderTextPreview = (testCase, wordSpaceMm, showBounds = false) => {
    if (!testCase || !testCase.text) return null;

    const scale = settings.canvasWidth / settings.canvasWidthMm;
    const letterSpacing = wordSpaceMm * scale;

    // 创建模拟的文本元素用于边界框显示
    const mockTextElement = {
      type: '1',
      content: testCase.text,
      char_pos: testCase.char_pos || null
    };

    return (
      <div style={{ position: 'relative' }}>
        <div
          style={{
            fontSize: `${settings.fontSize}px`,
            letterSpacing: `${letterSpacing}px`,
            padding: '10px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            backgroundColor: '#fafafa',
            fontFamily: 'Arial, sans-serif',
            textAlign: 'center',
            position: 'relative'
          }}
        >
          {testCase.text}
        </div>

        {/* 字符边界框覆盖层 */}
        {showBounds && testCase.char_pos && (
          <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
            <CharBoundingBoxOverlay
              textElements={[mockTextElement]}
              scale={scale * 0.1} // 调整缩放以匹配预览
              containerWidth={300}
              containerHeight={50}
              visible={showBounds}
              onVisibilityChange={() => {}}
            />
          </div>
        )}
      </div>
    );
  };

  // 测试结果表格列
  const resultColumns = [
    {
      title: '测试名称',
      dataIndex: 'name',
      key: 'name',
      width: 120
    },
    {
      title: '文本',
      dataIndex: 'text',
      key: 'text',
      width: 80
    },
    {
      title: '计算结果(mm)',
      key: 'calculated',
      width: 100,
      render: (_, record) => record.result?.wordSpaceMm?.toFixed(3) || '0.000'
    },
    {
      title: '期望值(mm)',
      dataIndex: 'expectedWordSpace',
      key: 'expected',
      width: 100,
      render: (value) => value?.toFixed(3) || '0.000'
    },
    {
      title: '准确率',
      dataIndex: 'accuracy',
      key: 'accuracy',
      width: 80,
      render: (value) => `${value}%`
    },
    {
      title: '计算方法',
      key: 'method',
      width: 120,
      render: (_, record) => record.result?.method || '-'
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 80
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>字间距效果验证页面</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* 设置面板 */}
        <Card title={<><SettingOutlined /> 测试设置</>} size="small">
          <Row gutter={16}>
            <Col span={6}>
              <Text strong>画布尺寸(px):</Text>
              <Input.Group compact>
                <Input
                  style={{ width: '50%' }}
                  value={settings.canvasWidth}
                  onChange={(e) => setSettings(prev => ({ ...prev, canvasWidth: parseInt(e.target.value) || 800 }))}
                  placeholder="宽度"
                />
                <Input
                  style={{ width: '50%' }}
                  value={settings.canvasHeight}
                  onChange={(e) => setSettings(prev => ({ ...prev, canvasHeight: parseInt(e.target.value) || 600 }))}
                  placeholder="高度"
                />
              </Input.Group>
            </Col>
            <Col span={6}>
              <Text strong>画布尺寸(mm):</Text>
              <Input.Group compact>
                <Input
                  style={{ width: '50%' }}
                  value={settings.canvasWidthMm}
                  onChange={(e) => setSettings(prev => ({ ...prev, canvasWidthMm: parseFloat(e.target.value) || 40 }))}
                  placeholder="宽度"
                />
                <Input
                  style={{ width: '50%' }}
                  value={settings.canvasHeightMm}
                  onChange={(e) => setSettings(prev => ({ ...prev, canvasHeightMm: parseFloat(e.target.value) || 30 }))}
                  placeholder="高度"
                />
              </Input.Group>
            </Col>
            <Col span={6}>
              <Text strong>缩放比例:</Text>
              <Input
                value={settings.scaleX}
                onChange={(e) => setSettings(prev => ({ ...prev, scaleX: parseFloat(e.target.value) || 0.1 }))}
                placeholder="像素到毫米"
              />
            </Col>
            <Col span={4}>
              <Text strong>字体大小:</Text>
              <Slider
                min={8}
                max={24}
                value={settings.fontSize}
                onChange={(value) => setSettings(prev => ({ ...prev, fontSize: value }))}
                marks={{ 8: '8px', 12: '12px', 16: '16px', 20: '20px', 24: '24px' }}
              />
            </Col>
            <Col span={2}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>字符边框:</Text>
                <Switch
                  checked={showCharBounds}
                  onChange={setShowCharBounds}
                  checkedChildren={<EyeOutlined />}
                  unCheckedChildren={<EyeInvisibleOutlined />}
                />
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 测试控制 */}
        <Card title="测试控制" size="small">
          <Space>
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={runAllTests}
            >
              运行所有预设测试
            </Button>
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => setTestResults([])}
            >
              清空结果
            </Button>
            <Text type="secondary">
              当前设置: {settings.canvasWidth}×{settings.canvasHeight}px ({settings.canvasWidthMm}×{settings.canvasHeightMm}mm)
            </Text>
          </Space>
        </Card>

        {/* 预设测试案例 */}
        <Card title="预设测试案例" size="small">
          <Row gutter={16}>
            {presetTests.map((test, index) => (
              <Col span={6} key={index}>
                <Card 
                  size="small" 
                  title={test.name}
                  extra={<Button size="small" onClick={() => runTest(test)}>测试</Button>}
                >
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Text><strong>文本:</strong> {test.text}</Text>
                    <Text><strong>期望字间距:</strong> {test.expectedWordSpace}mm</Text>
                    <Text><strong>字符数:</strong> {test.char_pos.length}</Text>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>

        {/* 当前测试结果 */}
        {currentTest && (
          <Card title={`当前测试结果: ${currentTest.name}`}>
            <Row gutter={16}>
              <Col span={12}>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Alert
                    type={currentTest.accuracy > 80 ? "success" : currentTest.accuracy > 60 ? "warning" : "error"}
                    message={
                      <Space direction="vertical" size="small">
                        <Text><strong>计算结果:</strong> {currentTest.result.wordSpaceMm.toFixed(3)}mm ({currentTest.result.wordSpacePx.toFixed(2)}px)</Text>
                        <Text><strong>期望值:</strong> {currentTest.expectedWordSpace.toFixed(3)}mm</Text>
                        <Text><strong>准确率:</strong> {currentTest.accuracy}%</Text>
                        <Text><strong>计算方法:</strong> {currentTest.result.method}</Text>
                      </Space>
                    }
                  />
                  
                  <div>
                    <Text strong>渲染效果预览:</Text>
                    <div style={{ marginTop: '8px' }}>
                      <Text>原始效果 (字间距: 0mm):</Text>
                      {renderTextPreview(currentTest, 0, showCharBounds)}
                    </div>
                    <div style={{ marginTop: '8px' }}>
                      <Text>计算效果 (字间距: {currentTest.result.wordSpaceMm.toFixed(3)}mm):</Text>
                      {renderTextPreview(currentTest, currentTest.result.wordSpaceMm, showCharBounds)}
                    </div>
                    <div style={{ marginTop: '8px' }}>
                      <Text>期望效果 (字间距: {currentTest.expectedWordSpace.toFixed(3)}mm):</Text>
                      {renderTextPreview(currentTest, currentTest.expectedWordSpace, showCharBounds)}
                    </div>
                  </div>
                </Space>
              </Col>
              <Col span={12}>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div>
                    <Text strong>详细信息:</Text>
                    <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                      <li>字符数量: {currentTest.result.details.charCount}</li>
                      <li>有效间距数: {currentTest.result.details.validGaps?.length || 0}</li>
                      <li>总间距: {currentTest.result.details.totalGap?.toFixed(2) || 0}px</li>
                      <li>平均间距: {currentTest.result.details.averageGap?.toFixed(2) || 0}px</li>
                      <li>缩放比例: {currentTest.settings.scaleX}</li>
                    </ul>
                  </div>
                </Space>
              </Col>
            </Row>
          </Card>
        )}

        {/* 测试历史 */}
        {testResults.length > 0 && (
          <Card title="测试历史">
            <Table
              dataSource={testResults}
              columns={resultColumns}
              pagination={false}
              size="small"
              rowKey={(record, index) => index}
              scroll={{ x: 800 }}
            />
          </Card>
        )}

        {/* 使用说明 */}
        <Card title="使用说明">
          <Paragraph>
            <Title level={5}>功能说明:</Title>
            <ul>
              <li><strong>预设测试:</strong> 包含不同类型的文本测试案例，验证字间距计算的准确性</li>
              <li><strong>实时预览:</strong> 显示原始效果、计算效果和期望效果的对比</li>
              <li><strong>参数调整:</strong> 可以调整画布尺寸、缩放比例和字体大小</li>
              <li><strong>准确率评估:</strong> 自动计算计算结果与期望值的准确率</li>
            </ul>
            
            <Title level={5}>测试案例说明:</Title>
            <ul>
              <li><strong>正常间距文本:</strong> 标准的字符间距，用于验证基本计算功能</li>
              <li><strong>紧密排列文本:</strong> 字符间距较小的文本，测试小间距的计算精度</li>
              <li><strong>宽间距文本:</strong> 字符间距较大的文本，测试大间距的处理能力</li>
              <li><strong>重叠字符:</strong> 字符重叠的情况，验证重叠检测和处理逻辑</li>
            </ul>
          </Paragraph>
        </Card>

      </Space>
    </div>
  );
};

export default WordSpaceValidationPage;
