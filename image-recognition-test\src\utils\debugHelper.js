/**
 * 调试辅助工具
 * 用于检查应用状态和API配置
 */

import textInApi from '../services/textinApi';

/**
 * 检查TextIn API配置
 */
export function checkTextInApiConfig() {
  console.log('=== TextIn API 配置检查 ===');
  
  try {
    // 检查凭据
    const hasCredentials = textInApi.appId && textInApi.secretCode;
    console.log('API凭据状态:', hasCredentials ? '已配置' : '未配置');
    
    if (hasCredentials) {
      console.log('App ID:', textInApi.appId ? '已设置' : '未设置');
      console.log('Secret Code:', textInApi.secretCode ? '已设置' : '未设置');
    } else {
      console.log('请在设置中配置TextIn API凭据');
    }
    
    // 检查localStorage中的凭据
    const savedCredentials = localStorage.getItem('textin-credentials');
    console.log('本地保存的凭据:', savedCredentials ? '存在' : '不存在');
    
    if (savedCredentials) {
      try {
        const parsed = JSON.parse(savedCredentials);
        console.log('本地凭据内容:', {
          hasAppId: !!parsed.appId,
          hasSecretCode: !!parsed.secretCode
        });
      } catch (error) {
        console.error('解析本地凭据失败:', error);
      }
    }
    
    return hasCredentials;
  } catch (error) {
    console.error('检查API配置时出错:', error);
    return false;
  }
}

/**
 * 检查应用状态
 */
export function checkAppStatus() {
  console.log('=== 应用状态检查 ===');
  
  // 检查必要的服务
  console.log('TextIn API服务:', typeof textInApi === 'object' ? '已加载' : '未加载');
  
  // 检查网络连接
  console.log('网络状态:', navigator.onLine ? '在线' : '离线');
  
  // 检查浏览器支持
  const features = {
    'File API': typeof File !== 'undefined',
    'FormData': typeof FormData !== 'undefined',
    'Fetch API': typeof fetch !== 'undefined',
    'Promise': typeof Promise !== 'undefined'
  };
  
  console.log('浏览器功能支持:');
  Object.entries(features).forEach(([feature, supported]) => {
    console.log(`  ${feature}: ${supported ? '支持' : '不支持'}`);
  });
  
  return features;
}

/**
 * 测试API连接
 */
export async function testApiConnection() {
  console.log('=== API连接测试 ===');
  
  const hasCredentials = checkTextInApiConfig();
  if (!hasCredentials) {
    console.error('无法测试API连接：缺少凭据');
    return false;
  }
  
  try {
    // 创建一个小的测试文件
    const testContent = 'Hello World';
    const testBlob = new Blob([testContent], { type: 'text/plain' });
    const testFile = new File([testBlob], 'test.txt', { type: 'text/plain' });
    
    console.log('发送测试请求...');
    
    // 注意：这里只是测试请求构建，不实际发送
    const url = textInApi.buildUrl();
    const headers = textInApi.getHeaders();
    
    console.log('请求URL:', url);
    console.log('请求头:', headers);
    console.log('测试文件:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });
    
    console.log('API连接配置正常');
    return true;
  } catch (error) {
    console.error('API连接测试失败:', error);
    return false;
  }
}

/**
 * 完整的诊断检查
 */
export async function runDiagnostics() {
  console.log('🔍 开始应用诊断...\n');
  
  const results = {
    appStatus: checkAppStatus(),
    apiConfig: checkTextInApiConfig(),
    apiConnection: await testApiConnection()
  };
  
  console.log('\n📊 诊断结果汇总:');
  console.log('应用状态:', results.appStatus ? '正常' : '异常');
  console.log('API配置:', results.apiConfig ? '正常' : '需要配置');
  console.log('API连接:', results.apiConnection ? '正常' : '异常');
  
  if (!results.apiConfig) {
    console.log('\n💡 解决建议:');
    console.log('1. 点击右上角的设置按钮');
    console.log('2. 输入您的TextIn API凭据');
    console.log('3. 保存设置后重试');
  }
  
  return results;
}

// 在浏览器环境中暴露调试函数
if (typeof window !== 'undefined') {
  window.debugHelper = {
    checkTextInApiConfig,
    checkAppStatus,
    testApiConnection,
    runDiagnostics
  };
  
  console.log('调试工具已加载，可以在控制台中调用:');
  console.log('- window.debugHelper.runDiagnostics() - 运行完整诊断');
  console.log('- window.debugHelper.checkTextInApiConfig() - 检查API配置');
  console.log('- window.debugHelper.checkAppStatus() - 检查应用状态');
}
