# 部署和使用指南

## 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0
- TextIn API账号和密钥

## 安装步骤

### 1. 解决PowerShell执行策略问题（Windows）

如果遇到PowerShell执行策略错误，请以管理员身份运行PowerShell并执行：

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

或者使用cmd命令行：

```cmd
cd backend
npm install
```

### 2. 安装依赖

```bash
cd backend
npm install
```

### 3. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置必要的配置：
```env
PORT=3001
TEXTIN_APP_ID=your_textin_app_id_here
TEXTIN_SECRET_CODE=your_textin_secret_code_here
```

### 4. 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

## 测试API

### 1. 健康检查

访问 `http://localhost:3001/health` 检查服务状态。

### 2. 使用测试脚本

将测试图片放在 `backend` 目录下，命名为 `test-image.jpg`，然后运行：

```bash
node test-api.js
```

### 3. 使用Postman测试

1. 创建POST请求到 `http://localhost:3001/api/recognize`
2. 在Body中选择 `form-data`
3. 添加字段 `image`，类型选择 `File`，上传图片文件
4. 发送请求

## API响应格式

### 成功响应

```json
{
  "success": true,
  "data": {
    "imageInfo": {
      "width": 1080,
      "height": 1920,
      "format": "jpeg"
    },
    "elements": [
      {
        "elementType": "1",
        "x": 100,
        "y": 200,
        "width": 300,
        "height": 50,
        "content": "示例文本",
        "charWidth": 25.5,
        "bold": false,
        "italic": false,
        "angle": 0
      }
    ]
  },
  "message": "识别成功"
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "RECOGNITION_FAILED",
    "message": "TextIn API识别失败: 具体错误信息"
  }
}
```

## 元素类型说明

### 文本元素 (elementType: "1")
- `x`, `y`: 位置坐标（像素）
- `width`, `height`: 尺寸（像素）
- `content`: 文本内容
- `charWidth`: 单个字符平均宽度（像素）
- `bold`: 是否粗体
- `italic`: 是否斜体
- `angle`: 旋转角度

### 条码元素 (elementType: "2")
- `x`, `y`: 位置坐标（像素）
- `width`, `height`: 尺寸（像素）
- `content`: 条码内容
- `barcodeType`: 条码类型（如CODE_128）
- `angle`: 旋转角度

### 二维码元素 (elementType: "7")
- `x`, `y`: 位置坐标（像素）
- `width`, `height`: 尺寸（像素）
- `content`: 二维码内容
- `angle`: 旋转角度

### 表格元素 (elementType: "10")
- `x`, `y`: 位置坐标（像素）
- `width`, `height`: 尺寸（像素）
- `rows`: 行数
- `cols`: 列数
- `cells`: 单元格数组
  - `row`, `col`: 单元格位置
  - `content`: 单元格内容
  - `charWidth`: 字符宽度
  - `bold`: 是否粗体

## 性能优化建议

1. **图片大小限制**: 建议上传图片不超过10MB
2. **图片格式**: 推荐使用JPEG格式，压缩比较好
3. **并发控制**: 生产环境建议使用PM2等进程管理器
4. **缓存策略**: 可以考虑对识别结果进行缓存

## 常见问题

### 1. TextIn API调用失败
- 检查API密钥是否正确
- 检查网络连接
- 检查图片格式是否支持

### 2. 条码识别失败
- 确保条码图像清晰
- 检查条码类型是否支持
- 尝试调整图片亮度和对比度

### 3. 内存使用过高
- 检查图片大小
- 确保及时清理临时文件
- 考虑使用图片压缩

## 生产环境部署

### 使用PM2

```bash
npm install -g pm2
pm2 start server.js --name "image-recognition-api"
pm2 startup
pm2 save
```

### 使用Docker

创建 `Dockerfile`:
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

构建和运行：
```bash
docker build -t image-recognition-api .
docker run -p 3001:3001 -e TEXTIN_APP_ID=your_app_id -e TEXTIN_SECRET_CODE=your_secret image-recognition-api
```
