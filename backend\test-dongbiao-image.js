const fs = require('fs');
const path = require('path');

// 加载环境变量
require('dotenv').config();

const TextInService = require('./services/textinService');
const DataConverter = require('./services/dataConverter');

/**
 * 测试解冻标签图片的粗体识别
 */
async function testDongbiaoImage() {
  console.log('🔍 测试解冻标签图片的粗体识别...\n');

  const imagePath = path.join(__dirname, 'data', '食品_解冻（60_40）.png');
  
  if (!fs.existsSync(imagePath)) {
    console.error('❌ 测试图片不存在:', imagePath);
    return;
  }

  console.log('📷 测试图片:', imagePath);
  
  try {
    // 1. 直接调用TextIn API
    console.log('\n🔧 步骤1: 调用TextIn API...');
    const textinService = new TextInService();
    const textinResult = await textinService.recognizeFile(imagePath);
    
    // 保存原始响应
    const rawOutputPath = path.join(__dirname, 'debug-dongbiao-raw.json');
    fs.writeFileSync(rawOutputPath, JSON.stringify(textinResult, null, 2));
    console.log(`💾 原始TextIn响应已保存到: ${rawOutputPath}`);
    
    // 2. 分析TextIn响应中的文本
    console.log('\n🔍 步骤2: 分析TextIn响应中的文本...');
    const pages = textinResult.result?.pages || [];
    if (pages.length > 0) {
      const content = pages[0].content || [];
      const textLines = content.filter(item => item.type === 'line');
      
      console.log(`找到 ${textLines.length} 个文本行:`);
      textLines.forEach((line, index) => {
        console.log(`\n文本行 ${index + 1}:`);
        console.log(`- 原始文本: "${line.text}"`);
        console.log(`- 是否包含**标记: ${line.text?.includes('**') ? '✅ 是' : '❌ 否'}`);
        
        if (line.text?.startsWith('**') && line.text?.endsWith('**')) {
          console.log(`- 🎯 检测到粗体标记!`);
        }
      });
    }
    
    // 3. 转换为APP格式
    console.log('\n🔄 步骤3: 转换为APP格式...');
    const dataConverter = new DataConverter();
    const imageInfo = { width: 720, height: 480, format: 'png' };
    const elements = await dataConverter.convertToAppFormat(textinResult, imageInfo);
    
    console.log(`✅ 转换完成，共生成 ${elements.length} 个元素\n`);
    
    // 4. 分析转换结果
    console.log('📊 转换结果分析:');
    elements.forEach((element, index) => {
      console.log(`\n元素 ${index + 1}:`);
      console.log(`- 类型: ${getElementTypeName(element.elementType)}`);
      console.log(`- 内容: "${element.content}"`);
      console.log(`- 粗体: ${element.bold ? '✅ 是' : '❌ 否'}`);
      console.log(`- 斜体: ${element.italic ? '✅ 是' : '❌ 否'}`);
      
      if (element.elementType === '1') {
        console.log(`- 字符宽度: ${element.charWidth}px`);
      }
    });

    // 5. 检查粗体识别结果
    console.log('\n🎯 粗体识别结果:');
    const boldElements = elements.filter(el => el.bold);
    if (boldElements.length > 0) {
      console.log(`✅ 发现 ${boldElements.length} 个粗体元素:`);
      boldElements.forEach((element, index) => {
        console.log(`${index + 1}. "${element.content}" (${getElementTypeName(element.elementType)})`);
      });
    } else {
      console.log('⚠️  没有发现粗体元素');
      console.log('💡 可能的原因:');
      console.log('1. TextIn API没有返回**标记');
      console.log('2. 图片中的文本确实不是粗体');
      console.log('3. 粗体检测逻辑需要调整');
    }

    // 保存转换结果
    const resultOutputPath = path.join(__dirname, 'debug-dongbiao-result.json');
    fs.writeFileSync(resultOutputPath, JSON.stringify({
      success: true,
      data: {
        imageInfo: imageInfo,
        elements: elements
      }
    }, null, 2));
    console.log(`\n💾 转换结果已保存到: ${resultOutputPath}`);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

/**
 * 获取元素类型名称
 */
function getElementTypeName(elementType) {
  const typeNames = {
    '1': '文本',
    '2': '一维码',
    '7': '二维码',
    '10': '表格'
  };
  return typeNames[elementType] || `未知(${elementType})`;
}

// 执行测试
testDongbiaoImage().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
