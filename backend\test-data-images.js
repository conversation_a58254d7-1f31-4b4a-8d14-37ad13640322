const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

/**
 * 测试data目录中的所有图片
 */
async function testDataImages() {
  const dataDir = path.join(__dirname, 'data');
  const apiUrl = 'http://localhost:3001/api/recognize';
  
  // 获取data目录中的所有图片文件
  const imageFiles = fs.readdirSync(dataDir).filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif', '.bmp'].includes(ext);
  });

  if (imageFiles.length === 0) {
    console.log('data目录中没有找到图片文件');
    return;
  }

  console.log(`找到 ${imageFiles.length} 张测试图片:`);
  imageFiles.forEach((file, index) => {
    console.log(`${index + 1}. ${file}`);
  });
  console.log('');

  // 逐一测试每张图片
  for (let i = 0; i < imageFiles.length; i++) {
    const imageFile = imageFiles[i];
    const imagePath = path.join(dataDir, imageFile);
    
    console.log(`\n${'='.repeat(60)}`);
    console.log(`测试图片 ${i + 1}/${imageFiles.length}: ${imageFile}`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      await testSingleImage(imagePath, imageFile, apiUrl);
    } catch (error) {
      console.error(`测试失败: ${error.message}`);
    }
    
    // 在测试之间添加短暂延迟
    if (i < imageFiles.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

/**
 * 测试单张图片
 */
async function testSingleImage(imagePath, fileName, apiUrl) {
  try {
    console.log(`文件路径: ${imagePath}`);
    
    // 获取文件信息
    const stats = fs.statSync(imagePath);
    console.log(`文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));
    
    console.log('发送识别请求...');
    const startTime = Date.now();
    
    // 发送请求
    const response = await axios.post(apiUrl, formData, {
      headers: {
        ...formData.getHeaders(),
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      timeout: 60000 // 60秒超时
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`请求完成! 耗时: ${duration}ms`);
    console.log(`状态码: ${response.status}`);
    
    // 检查响应格式
    if (response.data.success) {
      console.log('✅ 识别成功!');
      
      const { imageInfo, elements } = response.data.data;
      
      console.log('\n📷 图片信息:');
      console.log(`- 宽度: ${imageInfo.width}px`);
      console.log(`- 高度: ${imageInfo.height}px`);
      console.log(`- 格式: ${imageInfo.format}`);
      
      console.log(`\n🔍 识别结果: 共 ${elements.length} 个元素`);
      
      // 按类型统计元素
      const elementCounts = {
        '1': 0, // 文本
        '2': 0, // 条码
        '7': 0, // 二维码
        '10': 0 // 表格
      };
      
      elements.forEach(element => {
        elementCounts[element.elementType] = (elementCounts[element.elementType] || 0) + 1;
      });
      
      console.log('📊 元素类型统计:');
      console.log(`- 文本元素: ${elementCounts['1'] || 0}个`);
      console.log(`- 条码元素: ${elementCounts['2'] || 0}个`);
      console.log(`- 二维码元素: ${elementCounts['7'] || 0}个`);
      console.log(`- 表格元素: ${elementCounts['10'] || 0}个`);
      
      // 显示详细的元素信息
      if (elements.length > 0) {
        console.log('\n📝 详细元素信息:');
        
        elements.forEach((element, index) => {
          console.log(`\n${index + 1}. ${getElementTypeName(element.elementType)}:`);
          console.log(`   内容: "${element.content}"`);
          console.log(`   位置: (${element.x}, ${element.y})`);
          console.log(`   尺寸: ${element.width} x ${element.height}px`);
          
          if (element.elementType === '1') { // 文本元素
            console.log(`   字符宽度: ${element.charWidth}px`);
            console.log(`   粗体: ${element.bold}`);
            console.log(`   斜体: ${element.italic}`);
          } else if (element.elementType === '2') { // 条码元素
            console.log(`   条码类型: ${element.barcodeType}`);
          } else if (element.elementType === '10') { // 表格元素
            console.log(`   行数: ${element.rows}`);
            console.log(`   列数: ${element.cols}`);
            console.log(`   单元格数: ${element.cells.length}`);
          }
          
          if (element.angle && element.angle !== 0) {
            console.log(`   旋转角度: ${element.angle}°`);
          }
        });
      }
      
      // 保存响应数据到文件
      const outputFileName = `result_${fileName.replace(/\.[^/.]+$/, '')}.json`;
      const outputPath = path.join(__dirname, 'test-results', outputFileName);
      
      // 确保输出目录存在
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2));
      console.log(`\n💾 完整响应数据已保存到: ${outputPath}`);
      
    } else {
      console.error('❌ 识别失败:', response.data.error);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.response) {
      console.error('响应状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

/**
 * 获取元素类型名称
 */
function getElementTypeName(elementType) {
  const typeNames = {
    '1': '文本',
    '2': '一维码',
    '7': '二维码',
    '10': '表格'
  };
  return typeNames[elementType] || `未知类型(${elementType})`;
}

// 执行测试
console.log('🚀 开始测试data目录中的图片...\n');
testDataImages().then(() => {
  console.log('\n✅ 所有测试完成!');
}).catch(error => {
  console.error('\n❌ 测试过程中发生错误:', error.message);
});
