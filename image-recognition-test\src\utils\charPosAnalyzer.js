/**
 * TextIn API char_pos 数据分析工具
 * 用于分析字符位置数据结构和计算字间距
 */

/**
 * 分析char_pos数据结构
 * @param {Array} charPosArray - char_pos数组，每个元素是[x1,y1,x2,y2,x3,y3,x4,y4]
 * @param {string} text - 对应的文本内容
 * @param {Object} linePos - 整行的位置信息[x1,y1,x2,y2,x3,y3,x4,y4]
 * @returns {Object} 分析结果
 */
export function analyzeCharPos(charPosArray, text, linePos) {
  console.log('=== char_pos 数据分析 ===');
  console.log('文本内容:', text);
  console.log('字符数量:', text.length);
  console.log('char_pos数组长度:', charPosArray.length);
  console.log('整行位置:', linePos);

  const analysis = {
    text,
    charCount: text.length,
    charPosCount: charPosArray.length,
    characters: [],
    wordSpaces: [],
    totalWidth: 0,
    averageCharWidth: 0,
    averageWordSpace: 0
  };

  // 分析每个字符的边界框
  charPosArray.forEach((charPos, index) => {
    const char = text[index];
    const bounds = calculateCharBounds(charPos);
    
    const charInfo = {
      char,
      index,
      bounds,
      charPos,
      width: bounds.width,
      height: bounds.height,
      centerX: bounds.x + bounds.width / 2,
      centerY: bounds.y + bounds.height / 2
    };

    analysis.characters.push(charInfo);
  });

  // 计算字符间距
  for (let i = 0; i < analysis.characters.length - 1; i++) {
    const currentChar = analysis.characters[i];
    const nextChar = analysis.characters[i + 1];
    
    // 计算字符间的空隙
    const gap = calculateCharGap(currentChar, nextChar);
    analysis.wordSpaces.push(gap);
    
    console.log(`"${currentChar.char}" 到 "${nextChar.char}" 的间距:`, {
      当前字符右边界: currentChar.bounds.x + currentChar.bounds.width,
      下个字符左边界: nextChar.bounds.x,
      间距: gap.distance,
      重叠: gap.overlap
    });
  }

  // 计算统计信息
  analysis.totalWidth = analysis.characters.reduce((sum, char) => sum + char.width, 0);
  analysis.averageCharWidth = analysis.totalWidth / analysis.characters.length;
  
  if (analysis.wordSpaces.length > 0) {
    const totalWordSpace = analysis.wordSpaces.reduce((sum, gap) => sum + gap.distance, 0);
    analysis.averageWordSpace = totalWordSpace / analysis.wordSpaces.length;
  }

  // 验证与整行位置的一致性
  const lineBounds = calculateCharBounds(linePos);
  const firstCharLeft = analysis.characters[0]?.bounds.x || 0;
  const lastCharRight = analysis.characters[analysis.characters.length - 1];
  const calculatedLineWidth = lastCharRight ? 
    (lastCharRight.bounds.x + lastCharRight.bounds.width - firstCharLeft) : 0;

  console.log('=== 一致性验证 ===');
  console.log('整行边界框:', lineBounds);
  console.log('从字符计算的行宽:', calculatedLineWidth);
  console.log('行宽差异:', Math.abs(lineBounds.width - calculatedLineWidth));

  analysis.lineConsistency = {
    lineBounds,
    calculatedLineWidth,
    widthDifference: Math.abs(lineBounds.width - calculatedLineWidth),
    isConsistent: Math.abs(lineBounds.width - calculatedLineWidth) < 5 // 允许5像素误差
  };

  return analysis;
}

/**
 * 从char_pos坐标计算字符边界框
 * @param {Array} charPos - [x1,y1,x2,y2,x3,y3,x4,y4]
 * @returns {Object} 边界框 {x, y, width, height}
 */
export function calculateCharBounds(charPos) {
  if (!charPos || charPos.length !== 8) {
    return { x: 0, y: 0, width: 0, height: 0 };
  }

  const xs = [charPos[0], charPos[2], charPos[4], charPos[6]];
  const ys = [charPos[1], charPos[3], charPos[5], charPos[7]];

  const minX = Math.min(...xs);
  const maxX = Math.max(...xs);
  const minY = Math.min(...ys);
  const maxY = Math.max(...ys);

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  };
}

/**
 * 计算两个字符之间的间距
 * @param {Object} char1 - 第一个字符信息
 * @param {Object} char2 - 第二个字符信息
 * @returns {Object} 间距信息
 */
export function calculateCharGap(char1, char2) {
  const char1Right = char1.bounds.x + char1.bounds.width;
  const char2Left = char2.bounds.x;
  
  const distance = char2Left - char1Right;
  const overlap = distance < 0 ? Math.abs(distance) : 0;
  
  return {
    distance: Math.max(0, distance), // 负值表示重叠，设为0
    overlap,
    isOverlapping: distance < 0,
    char1Right,
    char2Left
  };
}

/**
 * 计算整行文本的平均字间距
 * @param {Array} charPosArray - char_pos数组
 * @param {string} text - 文本内容
 * @returns {number} 平均字间距（像素）
 */
export function calculateAverageWordSpace(charPosArray, text) {
  if (!charPosArray || charPosArray.length < 2) {
    return 0;
  }

  const characters = charPosArray.map((charPos, index) => ({
    char: text[index],
    bounds: calculateCharBounds(charPos)
  }));

  let totalGap = 0;
  let gapCount = 0;

  for (let i = 0; i < characters.length - 1; i++) {
    const gap = calculateCharGap(characters[i], characters[i + 1]);
    if (!gap.isOverlapping) {
      totalGap += gap.distance;
      gapCount++;
    }
  }

  return gapCount > 0 ? totalGap / gapCount : 0;
}

/**
 * 将像素字间距转换为毫米（用于XPrinter）
 * @param {number} pixelWordSpace - 像素字间距
 * @param {number} scaleX - X轴缩放比例（像素到毫米）
 * @returns {number} 毫米字间距
 */
export function convertWordSpaceToMm(pixelWordSpace, scaleX) {
  return pixelWordSpace * scaleX;
}

/**
 * 验证字间距计算的准确性
 * @param {Array} charPosArray - char_pos数组
 * @param {string} text - 文本内容
 * @param {Array} linePos - 整行位置
 * @returns {Object} 验证结果
 */
export function validateWordSpaceCalculation(charPosArray, text, linePos) {
  const analysis = analyzeCharPos(charPosArray, text, linePos);
  
  // 计算总宽度验证
  const totalCharWidth = analysis.characters.reduce((sum, char) => sum + char.width, 0);
  const totalWordSpace = analysis.wordSpaces.reduce((sum, gap) => sum + gap.distance, 0);
  const calculatedTotalWidth = totalCharWidth + totalWordSpace;
  
  const lineBounds = calculateCharBounds(linePos);
  const widthDifference = Math.abs(calculatedTotalWidth - lineBounds.width);
  
  return {
    analysis,
    validation: {
      totalCharWidth,
      totalWordSpace,
      calculatedTotalWidth,
      actualLineWidth: lineBounds.width,
      widthDifference,
      isAccurate: widthDifference < 5, // 允许5像素误差
      accuracyPercentage: ((lineBounds.width - widthDifference) / lineBounds.width * 100).toFixed(2)
    }
  };
}
