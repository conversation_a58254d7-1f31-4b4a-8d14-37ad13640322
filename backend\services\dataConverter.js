const BarcodeDecoder = require('./barcodeDecoder');

/**
 * 数据转换服务
 * 将TextIn API返回的数据转换为APP端需要的JSON格式
 */
class DataConverter {
  constructor() {
    this.barcodeDecoder = new BarcodeDecoder();
    
    // 元素类型常量
    this.ELEMENT_TYPES = {
      TEXT: '1',        // 文本
      BAR_CODE: '2',    // 一维码
      QR_CODE: '7',     // 二维码
      TABLE: '10'       // 表格
    };
  }

  /**
   * 转换TextIn数据为APP端格式
   * @param {Object} textinData - TextIn API返回的数据
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Array>} APP端格式的元素数组
   */
  async convertToAppFormat(textinData, imageInfo) {
    try {
      console.log('开始转换TextIn数据为APP格式');

      const { result } = textinData;
      if (!result || !result.pages) {
        throw new Error('TextIn数据格式不正确');
      }

      const elements = [];
      const firstPage = result.pages[0];
      
      if (!firstPage) {
        throw new Error('没有找到页面数据');
      }

      // 处理页面内容
      const pageContent = firstPage.content || [];
      const structuredData = result.structured || [];
      const detailData = result.detail || [];

      // 1. 合并detail和pages.content数据（detail提供格式，pages.content提供char_pos）
      const processedContentIds = new Set();

      // 检查是否有detail数据
      if (detailData.length > 0) {
        console.log(`合并detail数据和pages.content数据处理 ${detailData.length} 个项目`);

        for (let i = 0; i < detailData.length; i++) {
          const item = detailData[i];
          if (item.type === 'paragraph' && item.sub_type === 'text') {
            // 直接通过索引获取对应的pages.content项
            const matchingContentItem = pageContent[i] || null;
            const convertedElement = await this.convertMergedItem(item, matchingContentItem, imageInfo);
            if (convertedElement) {
              elements.push(convertedElement);
              // 标记对应的content项为已处理
              if (matchingContentItem) {
                processedContentIds.add(matchingContentItem.id);
                console.log(`通过索引 ${i} 匹配: detail["${item.text}"] <-> content["${matchingContentItem.text || 'N/A'}"]`);
              }
            }
          }
        }

        console.log('已使用合并的detail+content数据');
      }

      // 2. 处理结构化数据（表格等非文本块）
      for (const item of structuredData) {
        // 如果已经处理了detail数据，跳过textblock类型
        if (detailData.length > 0 && item.type === 'textblock') {
          continue;
        }
        const convertedElements = await this.convertStructuredItem(item, pageContent, processedContentIds, imageInfo);
        elements.push(...convertedElements);
      }

      // 3. 处理剩余的内容项（仅在没有detail数据时处理）
      if (detailData.length === 0) {
        console.log('没有detail数据，处理pages.content中的项目');
        for (const item of pageContent) {
          if (!processedContentIds.has(item.id)) {
            const convertedElement = await this.convertContentItem(item, imageInfo);
            if (convertedElement) {
              elements.push(convertedElement);
            }
          }
        }
      } else {
        console.log('已使用detail数据，跳过pages.content处理以避免重复');
      }

      console.log(`数据转换完成，共生成 ${elements.length} 个元素`);
      return elements;

    } catch (error) {
      console.error('数据转换失败:', error);
      throw new Error(`数据转换失败: ${error.message}`);
    }
  }



  /**
   * 转换合并的项（detail提供格式，content提供char_pos）
   * @param {Object} detailItem - detail项
   * @param {Object} contentItem - content项
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Object|null>} 转换后的元素
   */
  async convertMergedItem(detailItem, contentItem, imageInfo) {
    try {
      if (!detailItem.text || !detailItem.position) {
        return null;
      }

      const bounds = this.calculateBounds(detailItem.position);

      // 从detail获取文本样式（粗体、斜体）
      const textStyle = this.detectTextStyle(detailItem.text);

      // 从content获取字符宽度（如果有char_pos数据）
      let charWidth;
      if (contentItem && contentItem.char_pos) {
        charWidth = this.calculateCharWidth(contentItem);
        console.log(`使用content的char_pos计算字符宽度: ${charWidth}px`);
      } else {
        charWidth = this.estimateCharWidth(textStyle.cleanText, bounds.width);
        console.log(`估算字符宽度: ${charWidth}px`);
      }

      console.log(`处理合并项: "${detailItem.text}" -> "${textStyle.cleanText}", 粗体: ${textStyle.bold}`);

      return {
        elementType: this.ELEMENT_TYPES.TEXT,
        x: Math.round(bounds.x),
        y: Math.round(bounds.y),
        width: Math.round(bounds.width),
        height: Math.round(bounds.height),
        content: textStyle.cleanText,
        charWidth: charWidth,
        bold: textStyle.bold,
        italic: textStyle.italic,
        angle: detailItem.angle || 0
      };

    } catch (error) {
      console.error('转换合并项失败:', error);
      return null;
    }
  }

  /**
   * 转换结构化项
   * @param {Object} item - TextIn结构化项
   * @param {Array} pageContent - 页面内容数组
   * @param {Set} processedContentIds - 已处理内容ID集合
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Array>} 转换后的元素数组
   */
  async convertStructuredItem(item, pageContent, processedContentIds, imageInfo) {
    switch (item.type) {
      case 'paragraph':
        return await this.convertParagraph(item, pageContent, processedContentIds, imageInfo);
      case 'table':
        return [await this.convertTable(item, pageContent, processedContentIds, imageInfo)];
      default:
        console.warn('未知的结构化类型:', item.type);
        return [];
    }
  }

  /**
   * 转换内容项
   * @param {Object} item - TextIn内容项
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Object|null>} 转换后的元素
   */
  async convertContentItem(item, imageInfo) {
    switch (item.type) {
      case 'line':
        return this.convertTextLine(item, imageInfo);
      case 'image':
        return await this.convertImage(item, imageInfo);
      default:
        console.warn('未知的内容类型:', item.type);
        return null;
    }
  }

  /**
   * 转换段落
   * @param {Object} paragraph - TextIn段落数据
   * @param {Array} pageContent - 页面内容数组
   * @param {Set} processedContentIds - 已处理内容ID集合
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Array>} 文本元素数组
   */
  async convertParagraph(paragraph, pageContent, processedContentIds, imageInfo) {
    // 标记引用的内容为已处理
    if (paragraph.content) {
      const ids = Array.isArray(paragraph.content) ? paragraph.content : [paragraph.content];
      ids.forEach(id => processedContentIds.add(id));
    }

    const elements = [];
    const bounds = this.calculateBounds(paragraph.pos);

    // 处理多行文本
    if (paragraph.content && Array.isArray(paragraph.content) && paragraph.content.length > 1) {
      for (const contentId of paragraph.content) {
        const contentItem = pageContent.find(item => item.id === contentId);
        if (contentItem && contentItem.type === 'line') {
          const element = this.convertTextLine(contentItem, imageInfo);
          if (element) {
            elements.push(element);
          }
        }
      }
    } else {
      // 单行文本处理
      const textWithMarkdown = paragraph.markdown || paragraph.text || '';
      const textFragments = this.parseMarkdownText(textWithMarkdown.trim());

      // 获取字符宽度数据
      let charWidth = 0;
      if (paragraph.content && Array.isArray(paragraph.content) && paragraph.content.length === 1) {
        const contentItem = pageContent.find(item => item.id === paragraph.content[0]);
        if (contentItem) {
          charWidth = this.calculateCharWidth(contentItem);
        }
      }

      for (const fragment of textFragments) {
        elements.push({
          elementType: this.ELEMENT_TYPES.TEXT,
          x: Math.round(bounds.x),
          y: Math.round(bounds.y),
          width: Math.round(bounds.width),
          height: Math.round(bounds.height),
          content: fragment.text,
          charWidth: charWidth,
          bold: fragment.bold,
          italic: fragment.italic,
          angle: paragraph.angle || 0
        });
      }
    }

    return elements;
  }

  /**
   * 转换文本行
   * @param {Object} textLine - TextIn文本行数据
   * @param {Object} imageInfo - 图片信息
   * @returns {Object|null} 文本元素，如果内容为空则返回null
   */
  convertTextLine(textLine, imageInfo) {
    const content = textLine.text || '';

    // 过滤掉空内容的文本元素
    if (!content.trim()) {
      console.log(`跳过空内容的文本元素，位置: (${textLine.pos?.[0]}, ${textLine.pos?.[1]})`);
      return null;
    }

    const bounds = this.calculateBounds(textLine.pos);
    const charWidth = this.calculateCharWidth(textLine);

    // 检测粗体和斜体
    const textStyle = this.detectTextStyle(content);

    return {
      elementType: this.ELEMENT_TYPES.TEXT,
      x: Math.round(bounds.x),
      y: Math.round(bounds.y),
      width: Math.round(bounds.width),
      height: Math.round(bounds.height),
      content: textStyle.cleanText,
      charWidth: charWidth,
      bold: textStyle.bold,
      italic: textStyle.italic,
      angle: textLine.angle || 0
    };
  }

  /**
   * 转换图像（条码/二维码）
   * @param {Object} image - TextIn图像数据
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Object>} 图像元素
   */
  async convertImage(image, imageInfo) {
    const bounds = this.calculateBounds(image.pos);
    
    switch (image.sub_type) {
      case 'qrcode':
        const qrContent = await this.extractCodeContent(image, 'qrcode');
        return {
          elementType: this.ELEMENT_TYPES.QR_CODE,
          x: Math.round(bounds.x),
          y: Math.round(bounds.y),
          width: Math.round(bounds.width),
          height: Math.round(bounds.height),
          content: qrContent,
          angle: 0
        };

      case 'barcode':
        const barcodeContent = await this.extractCodeContent(image, 'barcode');
        return {
          elementType: this.ELEMENT_TYPES.BAR_CODE,
          x: Math.round(bounds.x),
          y: Math.round(bounds.y),
          width: Math.round(bounds.width),
          height: Math.round(bounds.height),
          content: barcodeContent,
          barcodeType: 'CODE_128', // 默认类型，可以根据实际情况调整
          angle: 0
        };

      default:
        // 其他类型的图像暂不处理
        return null;
    }
  }

  /**
   * 转换表格
   * @param {Object} table - TextIn表格数据
   * @param {Array} pageContent - 页面内容数组
   * @param {Set} processedContentIds - 已处理内容ID集合
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Object>} 表格元素
   */
  async convertTable(table, pageContent, processedContentIds, imageInfo) {
    const bounds = this.calculateBounds(table.pos);
    const cells = [];

    if (table.cells && Array.isArray(table.cells)) {
      for (const cell of table.cells) {
        let cellContent = cell.text || '';
        let charWidth = 0;

        // 如果单元格引用了内容项，获取详细信息
        if (cell.content && Array.isArray(cell.content)) {
          for (const contentId of cell.content) {
            processedContentIds.add(contentId);
            const contentItem = pageContent.find(item => item.id === contentId);
            if (contentItem) {
              cellContent = contentItem.text || cellContent;
              charWidth = this.calculateCharWidth(contentItem);
            }
          }
        }

        // 检测单元格文本样式
        const cellStyle = this.detectTextStyle(cellContent);

        cells.push({
          row: parseInt(cell.row) || 0,
          col: parseInt(cell.col) || 0,
          content: cellStyle.cleanText,
          charWidth: charWidth,
          bold: cellStyle.bold,
          italic: cellStyle.italic
        });
      }
    }

    return {
      elementType: this.ELEMENT_TYPES.TABLE,
      x: Math.round(bounds.x),
      y: Math.round(bounds.y),
      width: Math.round(bounds.width),
      height: Math.round(bounds.height),
      rows: table.rows || 0,
      cols: table.cols || 0,
      cells: cells
    };
  }

  /**
   * 计算边界框
   * @param {Array} pos - 位置数组 [x1,y1,x2,y2,x3,y3,x4,y4]
   * @returns {Object} 边界框 {x, y, width, height}
   */
  calculateBounds(pos) {
    if (!pos || pos.length < 8) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    const xs = [pos[0], pos[2], pos[4], pos[6]];
    const ys = [pos[1], pos[3], pos[5], pos[7]];

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * 计算字符宽度
   * @param {Object} contentItem - TextIn内容项
   * @returns {number} 优化后的字符宽度（像素）
   */
  calculateCharWidth(contentItem) {
    try {
      if (!contentItem.char_pos || !Array.isArray(contentItem.char_pos) || contentItem.char_pos.length === 0) {
        return 12; // 默认字符宽度
      }

      const charWidths = [];
      const text = contentItem.text || '';

      // 计算每个字符的宽度
      contentItem.char_pos.forEach((charPos, index) => {
        if (charPos && charPos.length === 8) {
          const xs = [charPos[0], charPos[2], charPos[4], charPos[6]];
          const minX = Math.min(...xs);
          const maxX = Math.max(...xs);
          const width = maxX - minX;

          if (width > 0) {
            const char = text[index] || '';
            charWidths.push({
              width: width,
              char: char,
              isChinese: this.isChinese(char),
              isNumber: /\d/.test(char),
              isPunctuation: /[：，。！？、；""''（）【】]/.test(char)
            });
          }
        }
      });

      if (charWidths.length === 0) {
        return 12;
      }

      // 使用优化策略计算字符宽度
      return this.calculateOptimizedCharWidth(charWidths);

    } catch (error) {
      console.error('字符宽度计算失败:', error);
      return 12;
    }
  }

  /**
   * 优化的字符宽度计算策略
   * @param {Array} charWidths - 字符宽度数组
   * @returns {number} 优化后的字符宽度
   */
  calculateOptimizedCharWidth(charWidths) {
    // 策略1: 优先使用中文字符的宽度（通常最稳定）
    const chineseChars = charWidths.filter(item => item.isChinese);
    if (chineseChars.length >= 2) {
      const avgChineseWidth = chineseChars.reduce((sum, item) => sum + item.width, 0) / chineseChars.length;
      console.log(`使用中文字符宽度: ${avgChineseWidth.toFixed(2)}px (${chineseChars.length}个中文字符)`);
      return Math.round(avgChineseWidth * 100) / 100;
    }

    // 策略2: 排除异常值，使用众数或中位数
    const widths = charWidths.map(item => item.width).sort((a, b) => a - b);

    // 计算四分位数，排除异常值
    const q1Index = Math.floor(widths.length * 0.25);
    const q3Index = Math.floor(widths.length * 0.75);
    const iqr = widths[q3Index] - widths[q1Index];
    const lowerBound = widths[q1Index] - 1.5 * iqr;
    const upperBound = widths[q3Index] + 1.5 * iqr;

    // 过滤异常值
    const filteredWidths = widths.filter(w => w >= lowerBound && w <= upperBound);

    if (filteredWidths.length > 0) {
      const avgWidth = filteredWidths.reduce((sum, w) => sum + w, 0) / filteredWidths.length;
      console.log(`使用过滤后的平均宽度: ${avgWidth.toFixed(2)}px (过滤掉${widths.length - filteredWidths.length}个异常值)`);
      return Math.round(avgWidth * 100) / 100;
    }

    // 策略3: 使用众数（出现频率最高的宽度）
    const widthCounts = {};
    widths.forEach(w => {
      const roundedWidth = Math.round(w);
      widthCounts[roundedWidth] = (widthCounts[roundedWidth] || 0) + 1;
    });

    const mostCommonWidth = Object.keys(widthCounts).reduce((a, b) =>
      widthCounts[a] > widthCounts[b] ? a : b
    );

    if (mostCommonWidth) {
      console.log(`使用众数宽度: ${mostCommonWidth}px (出现${widthCounts[mostCommonWidth]}次)`);
      return parseFloat(mostCommonWidth);
    }

    // 策略4: 简单平均（兜底方案）
    const avgWidth = widths.reduce((sum, w) => sum + w, 0) / widths.length;
    console.log(`使用简单平均宽度: ${avgWidth.toFixed(2)}px`);
    return Math.round(avgWidth * 100) / 100;
  }

  /**
   * 判断是否为中文字符
   * @param {string} char - 字符
   * @returns {boolean} 是否为中文
   */
  isChinese(char) {
    return /[\u4e00-\u9fff]/.test(char);
  }

  /**
   * 检测文本样式（粗体、斜体）
   * @param {string} text - 原始文本
   * @returns {Object} {cleanText, bold, italic}
   */
  detectTextStyle(text) {
    if (!text) {
      return { cleanText: '', bold: false, italic: false };
    }

    let cleanText = text;
    let bold = false;
    let italic = false;

    // 检测粗体 **text**
    if (text.startsWith('**') && text.endsWith('**') && text.length > 4) {
      bold = true;
      cleanText = text.slice(2, -2);
      console.log(`检测到粗体文本: "${text}" -> "${cleanText}"`);
    }
    // 检测斜体 *text* (但不是粗体)
    else if (text.startsWith('*') && text.endsWith('*') && text.length > 2) {
      italic = true;
      cleanText = text.slice(1, -1);
      console.log(`检测到斜体文本: "${text}" -> "${cleanText}"`);
    }

    return {
      cleanText: cleanText,
      bold: bold,
      italic: italic
    };
  }

  /**
   * 估算字符宽度（当没有char_pos数据时）
   * @param {string} text - 文本内容
   * @param {number} totalWidth - 文本总宽度
   * @returns {number} 估算的字符宽度
   */
  estimateCharWidth(text, totalWidth) {
    if (!text || text.length === 0) {
      return 12; // 默认值
    }

    // 简单估算：总宽度除以字符数
    const estimatedWidth = totalWidth / text.length;

    // 设置合理的范围
    const minWidth = 8;
    const maxWidth = 100;

    const charWidth = Math.max(minWidth, Math.min(maxWidth, estimatedWidth));
    return Math.round(charWidth * 100) / 100; // 保留2位小数
  }

  /**
   * 解析Markdown文本
   * @param {string} text - 包含Markdown标记的文本
   * @returns {Array} 文本片段数组
   */
  parseMarkdownText(text) {
    const fragments = [];
    const regex = /(\*\*.*?\*\*|\*.*?\*|[^*]+)/g;
    let match;

    while ((match = regex.exec(text)) !== null) {
      const fragment = match[1];
      
      if (fragment.startsWith('**') && fragment.endsWith('**')) {
        // 粗体文本
        fragments.push({
          text: fragment.slice(2, -2),
          bold: true,
          italic: false
        });
      } else if (fragment.startsWith('*') && fragment.endsWith('*')) {
        // 斜体文本
        fragments.push({
          text: fragment.slice(1, -1),
          bold: false,
          italic: true
        });
      } else {
        // 普通文本
        fragments.push({
          text: fragment,
          bold: false,
          italic: false
        });
      }
    }

    return fragments.length > 0 ? fragments : [{ text: text, bold: false, italic: false }];
  }

  /**
   * 提取条码/二维码内容
   * @param {Object} image - 图像数据
   * @param {string} type - 类型 ('barcode' 或 'qrcode')
   * @returns {Promise<string>} 解析出的内容
   */
  async extractCodeContent(image, type) {
    // 首先尝试从文本字段获取
    if (image.text && image.text.trim()) {
      return image.text.trim();
    }

    // 尝试从base64图像解析
    const base64Data = image.data?.base64 || image.base64str;
    if (base64Data) {
      try {
        const content = await this.barcodeDecoder.decode(base64Data, type);
        if (content) {
          return content;
        }
      } catch (error) {
        console.warn(`${type}解析失败:`, error.message);
      }
    }

    // 返回默认值
    return type === 'barcode' ? '[条码内容未识别]' : '[二维码内容未识别]';
  }
}

module.exports = DataConverter;
