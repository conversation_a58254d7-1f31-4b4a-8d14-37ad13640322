const BarcodeDecoder = require('./barcodeDecoder');

/**
 * 数据转换服务
 * 将TextIn API返回的数据转换为APP端需要的JSON格式
 */
class DataConverter {
  constructor() {
    this.barcodeDecoder = new BarcodeDecoder();
    
    // 元素类型常量
    this.ELEMENT_TYPES = {
      TEXT: '1',        // 文本
      BAR_CODE: '2',    // 一维码
      QR_CODE: '7',     // 二维码
      TABLE: '10'       // 表格
    };
  }

  /**
   * 转换TextIn数据为APP端格式
   * @param {Object} textinData - TextIn API返回的数据
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Array>} APP端格式的元素数组
   */
  async convertToAppFormat(textinData, imageInfo) {
    try {
      console.log('开始转换TextIn数据为APP格式');

      const { result } = textinData;
      if (!result || !result.pages) {
        throw new Error('TextIn数据格式不正确');
      }

      const elements = [];
      const firstPage = result.pages[0];
      
      if (!firstPage) {
        throw new Error('没有找到页面数据');
      }

      // 处理页面内容
      const pageContent = firstPage.content || [];
      const structuredData = result.structured || [];

      // 1. 处理结构化数据（段落、表格等）
      const processedContentIds = new Set();
      
      for (const item of structuredData) {
        const convertedElements = await this.convertStructuredItem(item, pageContent, processedContentIds, imageInfo);
        elements.push(...convertedElements);
      }

      // 2. 处理剩余的内容项（未被结构化数据引用的）
      for (const item of pageContent) {
        if (!processedContentIds.has(item.id)) {
          const convertedElement = await this.convertContentItem(item, imageInfo);
          if (convertedElement) {
            elements.push(convertedElement);
          }
        }
      }

      console.log(`数据转换完成，共生成 ${elements.length} 个元素`);
      return elements;

    } catch (error) {
      console.error('数据转换失败:', error);
      throw new Error(`数据转换失败: ${error.message}`);
    }
  }

  /**
   * 转换结构化项
   * @param {Object} item - TextIn结构化项
   * @param {Array} pageContent - 页面内容数组
   * @param {Set} processedContentIds - 已处理内容ID集合
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Array>} 转换后的元素数组
   */
  async convertStructuredItem(item, pageContent, processedContentIds, imageInfo) {
    switch (item.type) {
      case 'paragraph':
        return await this.convertParagraph(item, pageContent, processedContentIds, imageInfo);
      case 'table':
        return [await this.convertTable(item, pageContent, processedContentIds, imageInfo)];
      default:
        console.warn('未知的结构化类型:', item.type);
        return [];
    }
  }

  /**
   * 转换内容项
   * @param {Object} item - TextIn内容项
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Object|null>} 转换后的元素
   */
  async convertContentItem(item, imageInfo) {
    switch (item.type) {
      case 'line':
        return this.convertTextLine(item, imageInfo);
      case 'image':
        return await this.convertImage(item, imageInfo);
      default:
        console.warn('未知的内容类型:', item.type);
        return null;
    }
  }

  /**
   * 转换段落
   * @param {Object} paragraph - TextIn段落数据
   * @param {Array} pageContent - 页面内容数组
   * @param {Set} processedContentIds - 已处理内容ID集合
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Array>} 文本元素数组
   */
  async convertParagraph(paragraph, pageContent, processedContentIds, imageInfo) {
    // 标记引用的内容为已处理
    if (paragraph.content) {
      const ids = Array.isArray(paragraph.content) ? paragraph.content : [paragraph.content];
      ids.forEach(id => processedContentIds.add(id));
    }

    const elements = [];
    const bounds = this.calculateBounds(paragraph.pos);

    // 处理多行文本
    if (paragraph.content && Array.isArray(paragraph.content) && paragraph.content.length > 1) {
      for (const contentId of paragraph.content) {
        const contentItem = pageContent.find(item => item.id === contentId);
        if (contentItem && contentItem.type === 'line') {
          const element = this.convertTextLine(contentItem, imageInfo);
          if (element) {
            elements.push(element);
          }
        }
      }
    } else {
      // 单行文本处理
      const textWithMarkdown = paragraph.markdown || paragraph.text || '';
      const textFragments = this.parseMarkdownText(textWithMarkdown.trim());

      // 获取字符宽度数据
      let charWidth = 0;
      if (paragraph.content && Array.isArray(paragraph.content) && paragraph.content.length === 1) {
        const contentItem = pageContent.find(item => item.id === paragraph.content[0]);
        if (contentItem) {
          charWidth = this.calculateCharWidth(contentItem);
        }
      }

      for (const fragment of textFragments) {
        elements.push({
          elementType: this.ELEMENT_TYPES.TEXT,
          x: Math.round(bounds.x),
          y: Math.round(bounds.y),
          width: Math.round(bounds.width),
          height: Math.round(bounds.height),
          content: fragment.text,
          charWidth: charWidth,
          bold: fragment.bold,
          italic: fragment.italic,
          angle: paragraph.angle || 0
        });
      }
    }

    return elements;
  }

  /**
   * 转换文本行
   * @param {Object} textLine - TextIn文本行数据
   * @param {Object} imageInfo - 图片信息
   * @returns {Object} 文本元素
   */
  convertTextLine(textLine, imageInfo) {
    const bounds = this.calculateBounds(textLine.pos);
    const charWidth = this.calculateCharWidth(textLine);

    return {
      elementType: this.ELEMENT_TYPES.TEXT,
      x: Math.round(bounds.x),
      y: Math.round(bounds.y),
      width: Math.round(bounds.width),
      height: Math.round(bounds.height),
      content: textLine.text || '',
      charWidth: charWidth,
      bold: false,
      italic: false,
      angle: textLine.angle || 0
    };
  }

  /**
   * 转换图像（条码/二维码）
   * @param {Object} image - TextIn图像数据
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Object>} 图像元素
   */
  async convertImage(image, imageInfo) {
    const bounds = this.calculateBounds(image.pos);
    
    switch (image.sub_type) {
      case 'qrcode':
        const qrContent = await this.extractCodeContent(image, 'qrcode');
        return {
          elementType: this.ELEMENT_TYPES.QR_CODE,
          x: Math.round(bounds.x),
          y: Math.round(bounds.y),
          width: Math.round(bounds.width),
          height: Math.round(bounds.height),
          content: qrContent,
          angle: 0
        };

      case 'barcode':
        const barcodeContent = await this.extractCodeContent(image, 'barcode');
        return {
          elementType: this.ELEMENT_TYPES.BAR_CODE,
          x: Math.round(bounds.x),
          y: Math.round(bounds.y),
          width: Math.round(bounds.width),
          height: Math.round(bounds.height),
          content: barcodeContent,
          barcodeType: 'CODE_128', // 默认类型，可以根据实际情况调整
          angle: 0
        };

      default:
        // 其他类型的图像暂不处理
        return null;
    }
  }

  /**
   * 转换表格
   * @param {Object} table - TextIn表格数据
   * @param {Array} pageContent - 页面内容数组
   * @param {Set} processedContentIds - 已处理内容ID集合
   * @param {Object} imageInfo - 图片信息
   * @returns {Promise<Object>} 表格元素
   */
  async convertTable(table, pageContent, processedContentIds, imageInfo) {
    const bounds = this.calculateBounds(table.pos);
    const cells = [];

    if (table.cells && Array.isArray(table.cells)) {
      for (const cell of table.cells) {
        let cellContent = cell.text || '';
        let charWidth = 0;

        // 如果单元格引用了内容项，获取详细信息
        if (cell.content && Array.isArray(cell.content)) {
          for (const contentId of cell.content) {
            processedContentIds.add(contentId);
            const contentItem = pageContent.find(item => item.id === contentId);
            if (contentItem) {
              cellContent = contentItem.text || cellContent;
              charWidth = this.calculateCharWidth(contentItem);
            }
          }
        }

        cells.push({
          row: parseInt(cell.row) || 0,
          col: parseInt(cell.col) || 0,
          content: cellContent,
          charWidth: charWidth,
          bold: cellContent.startsWith('**') && cellContent.endsWith('**'),
          italic: false
        });
      }
    }

    return {
      elementType: this.ELEMENT_TYPES.TABLE,
      x: Math.round(bounds.x),
      y: Math.round(bounds.y),
      width: Math.round(bounds.width),
      height: Math.round(bounds.height),
      rows: table.rows || 0,
      cols: table.cols || 0,
      cells: cells
    };
  }

  /**
   * 计算边界框
   * @param {Array} pos - 位置数组 [x1,y1,x2,y2,x3,y3,x4,y4]
   * @returns {Object} 边界框 {x, y, width, height}
   */
  calculateBounds(pos) {
    if (!pos || pos.length < 8) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    const xs = [pos[0], pos[2], pos[4], pos[6]];
    const ys = [pos[1], pos[3], pos[5], pos[7]];

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * 计算字符宽度
   * @param {Object} contentItem - TextIn内容项
   * @returns {number} 平均字符宽度（像素）
   */
  calculateCharWidth(contentItem) {
    try {
      if (!contentItem.char_pos || !Array.isArray(contentItem.char_pos) || contentItem.char_pos.length === 0) {
        return 12; // 默认字符宽度
      }

      let totalWidth = 0;
      let validCharCount = 0;

      contentItem.char_pos.forEach(charPos => {
        if (charPos && charPos.length === 8) {
          const xs = [charPos[0], charPos[2], charPos[4], charPos[6]];
          const minX = Math.min(...xs);
          const maxX = Math.max(...xs);
          const width = maxX - minX;

          if (width > 0) {
            totalWidth += width;
            validCharCount++;
          }
        }
      });

      if (validCharCount === 0) {
        return 12;
      }

      const averageCharWidth = totalWidth / validCharCount;
      return Math.round(averageCharWidth * 100) / 100; // 保留2位小数
    } catch (error) {
      console.error('字符宽度计算失败:', error);
      return 12;
    }
  }

  /**
   * 解析Markdown文本
   * @param {string} text - 包含Markdown标记的文本
   * @returns {Array} 文本片段数组
   */
  parseMarkdownText(text) {
    const fragments = [];
    const regex = /(\*\*.*?\*\*|\*.*?\*|[^*]+)/g;
    let match;

    while ((match = regex.exec(text)) !== null) {
      const fragment = match[1];
      
      if (fragment.startsWith('**') && fragment.endsWith('**')) {
        // 粗体文本
        fragments.push({
          text: fragment.slice(2, -2),
          bold: true,
          italic: false
        });
      } else if (fragment.startsWith('*') && fragment.endsWith('*')) {
        // 斜体文本
        fragments.push({
          text: fragment.slice(1, -1),
          bold: false,
          italic: true
        });
      } else {
        // 普通文本
        fragments.push({
          text: fragment,
          bold: false,
          italic: false
        });
      }
    }

    return fragments.length > 0 ? fragments : [{ text: text, bold: false, italic: false }];
  }

  /**
   * 提取条码/二维码内容
   * @param {Object} image - 图像数据
   * @param {string} type - 类型 ('barcode' 或 'qrcode')
   * @returns {Promise<string>} 解析出的内容
   */
  async extractCodeContent(image, type) {
    // 首先尝试从文本字段获取
    if (image.text && image.text.trim()) {
      return image.text.trim();
    }

    // 尝试从base64图像解析
    const base64Data = image.data?.base64 || image.base64str;
    if (base64Data) {
      try {
        const content = await this.barcodeDecoder.decode(base64Data, type);
        if (content) {
          return content;
        }
      } catch (error) {
        console.warn(`${type}解析失败:`, error.message);
      }
    }

    // 返回默认值
    return type === 'barcode' ? '[条码内容未识别]' : '[二维码内容未识别]';
  }
}

module.exports = DataConverter;
