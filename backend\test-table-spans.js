const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

/**
 * 测试表格的rowSpan和colSpan功能
 */
async function testTableSpans() {
  console.log('🔍 测试表格的rowSpan和colSpan功能...\n');

  const imagePath = path.join(__dirname, 'data', '巧克力（100_70）.png');
  
  if (!fs.existsSync(imagePath)) {
    console.error('❌ 测试图片不存在:', imagePath);
    return;
  }

  console.log('📷 测试图片: 巧克力（100_70）.png');
  
  try {
    // 调用API
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));

    const response = await axios.post('http://localhost:3001/api/recognize', formData, {
      headers: {
        ...formData.getHeaders(),
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });

    if (response.data.success) {
      const { elements } = response.data.data;
      
      // 找到表格元素
      const tableElements = elements.filter(el => el.elementType === '10');
      
      if (tableElements.length === 0) {
        console.log('❌ 没有找到表格元素');
        return;
      }

      const table = tableElements[0];
      console.log(`✅ 找到表格: ${table.rows}行 x ${table.cols}列，包含 ${table.cells.length} 个单元格\n`);

      // 分析表格结构
      console.log('📊 表格结构分析:');
      console.log(`- 表格位置: (${table.x}, ${table.y})`);
      console.log(`- 表格尺寸: ${table.width} x ${table.height}`);
      console.log(`- 列宽: [${table.columnWidths.join(', ')}]`);
      console.log(`- 行高: [${table.rowHeights.join(', ')}]`);

      // 创建表格可视化
      console.log('\n📋 表格内容 (显示rowSpan和colSpan):');
      console.log('='.repeat(100));

      // 按行分组单元格
      const cellsByRow = {};
      table.cells.forEach(cell => {
        if (!cellsByRow[cell.row]) {
          cellsByRow[cell.row] = [];
        }
        cellsByRow[cell.row].push(cell);
      });

      // 显示每行的单元格
      for (let row = 0; row < table.rows; row++) {
        console.log(`\n第 ${row + 1} 行:`);
        const rowCells = cellsByRow[row] || [];
        rowCells.sort((a, b) => a.col - b.col);

        rowCells.forEach(cell => {
          const spanInfo = [];
          if (cell.rowSpan > 1) spanInfo.push(`rowSpan=${cell.rowSpan}`);
          if (cell.colSpan > 1) spanInfo.push(`colSpan=${cell.colSpan}`);
          const spanText = spanInfo.length > 0 ? ` (${spanInfo.join(', ')})` : '';
          
          console.log(`  列${cell.col + 1}: "${cell.content}"${spanText}`);
          if (cell.bold) console.log(`    ✨ 粗体文本`);
          if (cell.content.includes('<br>')) console.log(`    📄 多行文本`);
        });
      }

      // 分析合并单元格
      console.log('\n🔗 合并单元格分析:');
      const mergedCells = table.cells.filter(cell => cell.rowSpan > 1 || cell.colSpan > 1);
      
      if (mergedCells.length > 0) {
        console.log(`发现 ${mergedCells.length} 个合并单元格:`);
        mergedCells.forEach((cell, index) => {
          console.log(`\n${index + 1}. 位置: [行${cell.row + 1}, 列${cell.col + 1}]`);
          console.log(`   内容: "${cell.content}"`);
          console.log(`   跨行: ${cell.rowSpan} 行`);
          console.log(`   跨列: ${cell.colSpan} 列`);
          if (cell.rowSpan > 1 && cell.colSpan > 1) {
            console.log(`   📐 这是一个既跨行又跨列的复杂合并单元格`);
          } else if (cell.rowSpan > 1) {
            console.log(`   📏 这是一个跨行合并单元格`);
          } else if (cell.colSpan > 1) {
            console.log(`   📐 这是一个跨列合并单元格`);
          }
        });
      } else {
        console.log('没有发现合并单元格，所有单元格都是1x1');
      }

      // 生成HTML表格预览
      console.log('\n🌐 HTML表格预览:');
      console.log(generateHTMLTable(table));

      // 保存详细结果
      const detailResult = {
        tableInfo: {
          rows: table.rows,
          cols: table.cols,
          totalCells: table.cells.length,
          mergedCells: mergedCells.length,
          position: { x: table.x, y: table.y },
          size: { width: table.width, height: table.height }
        },
        cells: table.cells.map(cell => ({
          position: `[${cell.row + 1}, ${cell.col + 1}]`,
          content: cell.content,
          rowSpan: cell.rowSpan,
          colSpan: cell.colSpan,
          bold: cell.bold,
          italic: cell.italic,
          charWidth: cell.charWidth,
          isMerged: cell.rowSpan > 1 || cell.colSpan > 1
        }))
      };

      const outputPath = path.join(__dirname, 'debug-table-spans-result.json');
      fs.writeFileSync(outputPath, JSON.stringify(detailResult, null, 2));
      console.log(`\n💾 详细结果已保存到: ${outputPath}`);

    } else {
      console.error('❌ 识别失败:', response.data.error);
    }

  } catch (error) {
    console.error('❌ API调用失败:', error.message);
  }
}

/**
 * 生成HTML表格预览
 */
function generateHTMLTable(table) {
  let html = '<table border="1" style="border-collapse: collapse;">\n';
  
  // 按行分组单元格
  const cellsByRow = {};
  table.cells.forEach(cell => {
    if (!cellsByRow[cell.row]) {
      cellsByRow[cell.row] = [];
    }
    cellsByRow[cell.row].push(cell);
  });

  // 生成每行
  for (let row = 0; row < table.rows; row++) {
    html += '  <tr>\n';
    const rowCells = cellsByRow[row] || [];
    rowCells.sort((a, b) => a.col - b.col);

    rowCells.forEach(cell => {
      const rowSpanAttr = cell.rowSpan > 1 ? ` rowspan="${cell.rowSpan}"` : '';
      const colSpanAttr = cell.colSpan > 1 ? ` colspan="${cell.colSpan}"` : '';
      const styleAttr = cell.bold ? ' style="font-weight: bold;"' : '';
      const content = cell.content.replace(/<br>/g, '<br/>');
      
      html += `    <td${rowSpanAttr}${colSpanAttr}${styleAttr}>${content}</td>\n`;
    });
    
    html += '  </tr>\n';
  }
  
  html += '</table>';
  return html;
}

// 执行测试
testTableSpans().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
