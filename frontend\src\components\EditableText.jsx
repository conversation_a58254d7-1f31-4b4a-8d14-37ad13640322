import React, { useState, useRef, useEffect } from 'react';
import { Input, Typography } from 'antd';

const { Text } = Typography;

/**
 * 渲染包含HTML标签的文本，支持换行
 * @param {string} text - 包含HTML标签的文本
 * @returns {React.ReactNode} 渲染后的内容
 */
const renderTextWithLineBreaks = (text) => {
  if (!text) return null;

  // 处理 <br> 标签，将其转换为换行
  const parts = text.split(/<br\s*\/?>/gi);

  return parts.map((part, index) => (
    <React.Fragment key={index}>
      {part}
      {index < parts.length - 1 && <br />}
    </React.Fragment>
  ));
};

/**
 * 渲染拉伸模式的文本，支持HTML标签
 * @param {string} text - 文本内容
 * @param {Object} textStyle - 文本样式
 * @returns {React.ReactNode} 渲染后的内容
 */
const renderStretchText = (text, textStyle) => {
  if (!text) return null;

  // 处理 <br> 标签
  const parts = text.split(/<br\s*\/?>/gi);

  return parts.map((part, partIndex) => (
    <React.Fragment key={partIndex}>
      {part.split('').map((char, charIndex) => (
        <span key={`${partIndex}-${charIndex}`} style={{
          display: 'inline-block',
          textAlign: 'center',
          flexShrink: 0,
          flexGrow: 0,
          ...textStyle
        }}>
          {char}
        </span>
      ))}
      {partIndex < parts.length - 1 && (
        <div key={`br-${partIndex}`} style={{ width: '100%', height: 0 }} />
      )}
    </React.Fragment>
  ));
};

const EditableText = ({
  value = '',
  onChange,
  style = {},
  textStyle = {},
  placeholder = '点击编辑文本',
  maxLength = 200,
  disabled = false,
  stretch = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const inputRef = useRef(null);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleClick = () => {
    if (!disabled) {
      setIsEditing(true);
    }
  };

  const handleSave = () => {
    setIsEditing(false);
    if (onChange && editValue !== value) {
      onChange(editValue);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleBlur = () => {
    handleSave();
  };

  const handleChange = (e) => {
    setEditValue(e.target.value);
  };

  if (isEditing) {
    return (
      <Input
        ref={inputRef}
        value={editValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        maxLength={maxLength}
        style={{
          border: '1px solid #1890ff',
          borderRadius: '2px',
          padding: '2px 4px',
          fontSize: 'inherit',
          fontFamily: 'inherit',
          fontWeight: 'inherit',
          color: 'inherit',
          background: '#fff',
          width: '100%',
          height: '100%',
          ...style
        }}
      />
    );
  }

  if (stretch && value) {
    return (
      <div
        onClick={handleClick}
        style={{
          cursor: disabled ? 'default' : 'pointer',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '2px 4px',
          borderRadius: '2px',
          transition: 'all 0.2s ease',
          overflow: 'hidden',
          ...style,
        }}
        title={disabled ? '' : '点击编辑文本'}
      >
        {renderStretchText(value, textStyle)}
      </div>
    );
  }

  return (
    <div
      onClick={handleClick}
      style={{
        cursor: disabled ? 'default' : 'pointer',
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'inherit',
        padding: '2px 4px',
        borderRadius: '2px',
        transition: 'all 0.2s ease',
        ...style,
        ...(disabled ? {} : {
          ':hover': {
            backgroundColor: 'rgba(24, 144, 255, 0.1)',
            border: '1px dashed #1890ff'
          }
        })
      }}
      title={disabled ? '' : '点击编辑文本'}
    >
      <Text
        style={{
          fontSize: 'inherit',
          fontFamily: 'inherit',
          fontWeight: 'inherit',
          color: 'inherit',
          lineHeight: '1.2',
          wordBreak: 'break-all',
          width: '100%',
          textAlign: 'inherit',
          ...textStyle
        }}
      >
        {renderTextWithLineBreaks(value || placeholder)}
      </Text>
    </div>
  );
};

export default EditableText;
