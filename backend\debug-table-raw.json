{"metrics": [{"page_image_height": 840, "page_image_width": 1200, "status": "Success", "angle": 0, "duration": 443.8504638671875, "page_id": 1}], "result_count": 1, "msg": "success", "result": {"detail": [{"outline_level": -1, "paragraph_id": 0, "sub_type": "text", "tags": [], "text": "**扁桃仁开心果牛奶巧克力**", "content": 0, "page_id": 1, "position": [213, 43, 1000, 42, 999, 111, 212, 113], "type": "paragraph"}, {"content": 0, "paragraph_id": 1, "tags": [], "text": "配料：白砂储、可可快、可可，被物法、脱腻奶粉、乳，清粉、开心果奶油《白砂糖、横物法、脱思奶粉、开心果、乳清制）。房桃仁奶油《白矽德、植物法、脱奶粉、腐桃仁、乳，清标）。食物添加剂《模测、食用香F）。可可含量不低于31％", "type": "paragraph", "outline_level": -1, "page_id": 1, "position": [40, 137, 1164, 137, 1164, 316, 40, 316], "sub_type": "text"}, {"page_id": 1, "position": [41, 327, 289, 327, 289, 363, 41, 363], "tags": [], "type": "paragraph", "content": 0, "outline_level": -1, "paragraph_id": 2, "sub_type": "text", "text": "原产国：意大利"}, {"content": 0, "outline_level": -1, "paragraph_id": 3, "position": [41, 374, 476, 374, 476, 410, 41, 410], "sub_type": "text", "text": "生厂日期：2022年01月12日", "page_id": 1, "tags": [], "type": "paragraph"}, {"page_id": 1, "position": [40, 421, 727, 421, 727, 457, 40, 457], "text": "贮存方式：置放闹凉干燥处，避免阳光直酒", "type": "paragraph", "content": 0, "outline_level": -1, "paragraph_id": 4, "sub_type": "text", "tags": []}, {"paragraph_id": 5, "text": "保质期至：2023年01月23日", "page_id": 1, "position": [41, 471, 477, 471, 477, 507, 41, 507], "sub_type": "text", "tags": [], "type": "paragraph", "content": 0, "outline_level": -1}, {"paragraph_id": 6, "text": "生产商：xxx", "content": 0, "outline_level": -1, "page_id": 1, "position": [41, 518, 234, 519, 233, 559, 41, 557], "sub_type": "text", "tags": [], "type": "paragraph"}, {"outline_level": -1, "page_id": 1, "paragraph_id": 7, "sub_type": "text", "text": "国内总经销商：xxx", "type": "paragraph", "content": 0, "position": [41, 565, 347, 565, 347, 601, 41, 601], "tags": []}, {"tags": [], "type": "paragraph", "content": 0, "outline_level": -1, "paragraph_id": 8, "sub_type": "text", "page_id": 1, "position": [40, 612, 194, 615, 195, 648, 41, 646], "text": "地址：xxx"}, {"cells": [{"col": 0, "col_span": 3, "page_id": 1, "position": [522, 467, 1151, 467, 1151, 546, 522, 546], "row": 0, "row_span": 1, "text": "营养成分表", "type": "cell"}, {"position": [522, 546, 798, 546, 798, 599, 522, 599], "row": 1, "row_span": 1, "text": "项目", "type": "cell", "col": 0, "col_span": 1, "page_id": 1}, {"text": "每100g ", "type": "cell", "col": 1, "col_span": 1, "page_id": 1, "position": [798, 546, 988, 546, 988, 599, 798, 599], "row": 1, "row_span": 1}, {"col": 2, "col_span": 1, "page_id": 1, "position": [988, 546, 1151, 546, 1151, 599, 988, 599], "row": 1, "row_span": 1, "text": "NRV%", "type": "cell"}, {"col_span": 1, "page_id": 1, "position": [522, 599, 798, 599, 798, 646, 522, 646], "row": 2, "row_span": 1, "text": "能量", "type": "cell", "col": 0}, {"row_span": 1, "text": "912kJ ", "type": "cell", "col": 1, "col_span": 1, "page_id": 1, "position": [798, 599, 988, 599, 988, 646, 798, 646], "row": 2}, {"row_span": 1, "text": "11%", "type": "cell", "col": 2, "col_span": 1, "page_id": 1, "position": [988, 599, 1151, 599, 1151, 646, 988, 646], "row": 2}, {"col_span": 1, "page_id": 1, "position": [522, 646, 798, 646, 798, 692, 522, 692], "row": 3, "row_span": 1, "text": "蛋白质", "type": "cell", "col": 0}, {"page_id": 1, "position": [798, 646, 988, 646, 988, 692, 798, 692], "row": 3, "row_span": 1, "text": "7.4g ", "type": "cell", "col": 1, "col_span": 1}, {"row_span": 1, "text": "12%", "type": "cell", "col": 2, "col_span": 1, "page_id": 1, "position": [988, 646, 1151, 646, 1151, 692, 988, 692], "row": 3}, {"col_span": 1, "page_id": 1, "position": [522, 692, 798, 692, 798, 799, 522, 799], "row": 4, "row_span": 2, "text": "脂肪<br>碳水化合物", "type": "cell", "col": 0}, {"col_span": 1, "page_id": 1, "position": [798, 692, 988, 692, 988, 799, 798, 799], "row": 4, "row_span": 2, "text": "4.8g<br>35.8g ", "type": "cell", "col": 1}, {"page_id": 1, "position": [988, 692, 1151, 692, 1151, 739, 988, 739], "row": 4, "row_span": 1, "text": "8%", "type": "cell", "col": 2, "col_span": 1}, {"row": 5, "row_span": 1, "text": "12%", "type": "cell", "col": 2, "col_span": 1, "page_id": 1, "position": [988, 739, 1151, 739, 1151, 799, 988, 799]}], "page_id": 1, "position": [521, 465, 1159, 470, 1158, 807, 520, 802], "content": 0, "outline_level": -1, "paragraph_id": 9, "sub_type": "bordered", "text": "<table border=\"1\" ><tr>\n<td colspan=\"3\" rowspan=\"1\">营养成分表</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">项目</td>\n<td colspan=\"1\" rowspan=\"1\">每100g </td>\n<td colspan=\"1\" rowspan=\"1\">NRV%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">能量</td>\n<td colspan=\"1\" rowspan=\"1\">912kJ </td>\n<td colspan=\"1\" rowspan=\"1\">11%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">蛋白质</td>\n<td colspan=\"1\" rowspan=\"1\">7.4g </td>\n<td colspan=\"1\" rowspan=\"1\">12%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"2\">脂肪<br>碳水化合物</td>\n<td colspan=\"1\" rowspan=\"2\">4.8g<br>35.8g </td>\n<td colspan=\"1\" rowspan=\"1\">8%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">12%</td>\n</tr></table>", "type": "table"}, {"text": "电话：xx", "content": 0, "position": [40, 659, 175, 661, 176, 697, 41, 695], "sub_type": "text", "tags": [], "outline_level": -1, "page_id": 1, "paragraph_id": 10, "type": "paragraph"}, {"position": [41, 708, 176, 708, 176, 743, 41, 743], "sub_type": "text", "tags": [], "outline_level": -1, "page_id": 1, "paragraph_id": 11, "text": "传真：xx", "type": "paragraph", "content": 0}, {"type": "paragraph", "paragraph_id": 12, "position": [40, 755, 251, 755, 251, 790, 40, 790], "tags": [], "sub_type": "text", "text": "净重量120克", "content": 0, "outline_level": -1, "page_id": 1}], "markdown": "**扁桃仁开心果牛奶巧克力**\n\n配料：白砂储、可可快、可可，被物法、脱腻奶粉、乳，清粉、开心果奶油《白砂糖、横物法、脱思奶粉、开心果、乳清制）。房桃仁奶油《白矽德、植物法、脱奶粉、腐桃仁、乳，清标）。食物添加剂《模测、食用香F）。可可含量不低于31％\n\n原产国：意大利\n\n生厂日期：2022年01月12日\n\n贮存方式：置放闹凉干燥处，避免阳光直酒\n\n保质期至：2023年01月23日\n\n生产商：xxx\n\n国内总经销商：xxx\n\n地址：xxx\n\n<table border=\"1\" ><tr>\n<td colspan=\"3\" rowspan=\"1\">营养成分表</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">项目</td>\n<td colspan=\"1\" rowspan=\"1\">每100g </td>\n<td colspan=\"1\" rowspan=\"1\">NRV%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">能量</td>\n<td colspan=\"1\" rowspan=\"1\">912kJ </td>\n<td colspan=\"1\" rowspan=\"1\">11%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">蛋白质</td>\n<td colspan=\"1\" rowspan=\"1\">7.4g </td>\n<td colspan=\"1\" rowspan=\"1\">12%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"2\">脂肪<br>碳水化合物</td>\n<td colspan=\"1\" rowspan=\"2\">4.8g<br>35.8g </td>\n<td colspan=\"1\" rowspan=\"1\">8%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">12%</td>\n</tr></table>\n\n电话：xx\n\n传真：xx\n\n净重量120克\n\n", "pages": [{"content": [{"type": "line", "char_pos": [[213, 43, 279, 43, 278, 110, 213, 109], [285, 44, 353, 45, 353, 110, 285, 110], [379, 57, 423, 57, 423, 68, 380, 68], [429, 45, 495, 46, 495, 111, 429, 110], [500, 67, 516, 67, 516, 96, 500, 96], [574, 46, 639, 46, 639, 110, 574, 110], [646, 45, 713, 45, 712, 111, 646, 111], [715, 45, 781, 45, 781, 112, 715, 112], [787, 50, 820, 50, 819, 97, 788, 97], [861, 44, 927, 44, 927, 112, 861, 112], [934, 44, 997, 44, 997, 110, 934, 110]], "id": 0, "pos": [213, 43, 1000, 42, 1000, 113, 213, 115], "score": 0.9990000128746033, "text": "扁桃仁开心果牛奶巧克力"}, {"score": 0.9990000128746033, "text": "配料：白砂储、可可快、可可，被物法、脱腻奶粉、乳，清粉、开心果", "type": "line", "char_pos": [[41, 137, 76, 138, 76, 173, 41, 173], [78, 138, 116, 138, 116, 174, 78, 174], [121, 151, 130, 151, 129, 170, 121, 170], [139, 138, 169, 138, 169, 173, 139, 173], [173, 138, 210, 138, 209, 173, 173, 173], [211, 137, 249, 138, 249, 174, 211, 174], [253, 159, 263, 159, 262, 171, 253, 171], [289, 141, 322, 141, 322, 174, 289, 174], [327, 140, 360, 140, 360, 173, 327, 173], [363, 137, 401, 138, 400, 174, 364, 174], [405, 159, 415, 159, 415, 171, 405, 171], [441, 141, 474, 141, 474, 173, 441, 173], [480, 141, 513, 141, 513, 174, 480, 174], [517, 165, 523, 165, 523, 173, 517, 173], [535, 138, 571, 138, 571, 174, 535, 174], [573, 138, 609, 138, 609, 174, 573, 174], [611, 138, 647, 138, 647, 174, 611, 174], [653, 160, 662, 160, 662, 171, 653, 171], [687, 138, 723, 138, 723, 174, 687, 174], [725, 139, 761, 139, 760, 174, 725, 174], [763, 138, 798, 138, 798, 174, 763, 174], [800, 139, 837, 139, 837, 174, 800, 174], [843, 159, 852, 160, 852, 171, 843, 171], [876, 138, 911, 138, 911, 174, 877, 174], [916, 164, 922, 165, 922, 173, 916, 173], [933, 138, 969, 138, 969, 174, 934, 174], [971, 138, 1008, 139, 1008, 175, 971, 175], [1014, 159, 1024, 159, 1023, 172, 1014, 172], [1047, 139, 1083, 139, 1083, 174, 1047, 174], [1087, 138, 1121, 138, 1121, 173, 1087, 173], [1124, 139, 1159, 139, 1159, 173, 1124, 173]], "id": 1, "pos": [40, 136, 1164, 136, 1164, 177, 40, 177]}, {"pos": [40, 184, 1145, 184, 1145, 225, 40, 225], "score": 0.9990000128746033, "text": "奶油《白砂糖、横物法、脱思奶粉、开心果、乳清制）。房桃仁奶", "type": "line", "char_pos": [[41, 185, 76, 185, 76, 221, 41, 221], [79, 186, 114, 186, 114, 221, 79, 221], [152, 184, 172, 184, 172, 222, 152, 222], [177, 185, 207, 185, 207, 220, 177, 220], [211, 185, 247, 185, 247, 220, 211, 220], [249, 185, 286, 185, 286, 221, 249, 221], [292, 206, 301, 206, 301, 218, 292, 218], [324, 185, 361, 185, 361, 221, 324, 221], [363, 185, 399, 185, 399, 221, 363, 221], [402, 185, 438, 185, 438, 221, 402, 221], [443, 206, 453, 206, 453, 218, 443, 218], [477, 185, 514, 185, 514, 221, 477, 221], [516, 186, 551, 186, 550, 220, 516, 220], [553, 185, 589, 185, 589, 221, 554, 221], [591, 186, 628, 186, 627, 221, 592, 221], [633, 206, 643, 206, 643, 218, 633, 218], [668, 187, 704, 187, 703, 221, 668, 221], [707, 186, 741, 186, 741, 220, 707, 220], [744, 186, 780, 187, 779, 220, 745, 220], [785, 206, 795, 206, 795, 218, 785, 218], [819, 185, 854, 185, 854, 221, 819, 221], [858, 186, 893, 186, 893, 220, 858, 220], [895, 186, 927, 186, 927, 221, 895, 221], [932, 187, 945, 187, 944, 220, 932, 220], [957, 208, 967, 208, 966, 217, 957, 217], [991, 185, 1027, 185, 1026, 221, 991, 221], [1028, 185, 1065, 185, 1064, 221, 1028, 221], [1067, 185, 1102, 185, 1101, 220, 1067, 220], [1104, 185, 1141, 185, 1140, 221, 1104, 221]], "id": 2}, {"score": 0.9990000128746033, "text": "油《白矽德、植物法、脱奶粉、腐桃仁、乳，清标）。食物添加剂《", "type": "line", "char_pos": [[42, 233, 76, 233, 76, 268, 42, 268], [95, 232, 115, 232, 115, 268, 95, 269], [120, 232, 150, 232, 150, 267, 120, 267], [154, 232, 191, 232, 190, 267, 155, 267], [192, 232, 229, 232, 229, 267, 192, 267], [234, 253, 244, 253, 244, 265, 234, 265], [267, 232, 304, 232, 304, 268, 267, 268], [306, 232, 342, 232, 342, 268, 306, 268], [345, 232, 381, 232, 381, 268, 345, 268], [386, 253, 396, 253, 396, 265, 386, 265], [421, 232, 457, 232, 457, 268, 421, 268], [459, 232, 494, 232, 494, 268, 459, 268], [496, 233, 533, 233, 532, 269, 496, 269], [539, 254, 548, 254, 548, 265, 539, 265], [573, 233, 608, 233, 608, 267, 574, 267], [610, 232, 646, 232, 646, 268, 610, 268], [649, 232, 684, 233, 684, 267, 650, 267], [690, 254, 700, 254, 700, 265, 690, 265], [725, 232, 759, 233, 758, 268, 725, 268], [764, 259, 769, 259, 769, 267, 764, 267], [782, 232, 817, 232, 817, 268, 782, 268], [819, 232, 856, 232, 855, 268, 819, 268], [857, 234, 869, 234, 869, 267, 857, 267], [880, 255, 890, 255, 890, 264, 880, 265], [915, 232, 950, 232, 950, 268, 915, 268], [952, 232, 988, 232, 988, 268, 952, 268], [990, 232, 1027, 232, 1027, 268, 991, 268], [1029, 233, 1063, 233, 1062, 268, 1029, 268], [1067, 231, 1101, 231, 1101, 268, 1067, 268], [1121, 231, 1141, 232, 1141, 269, 1122, 269]], "id": 3, "pos": [41, 231, 1145, 231, 1145, 272, 41, 272]}, {"id": 4, "pos": [41, 279, 671, 279, 671, 320, 41, 320], "score": 0.9990000128746033, "text": "模测、食用香F）。可可含量不低于31％", "type": "line", "char_pos": [[41, 280, 77, 280, 77, 316, 41, 316], [78, 281, 114, 281, 114, 316, 79, 316], [120, 301, 130, 301, 130, 313, 120, 313], [155, 280, 191, 280, 190, 316, 155, 316], [193, 281, 226, 281, 226, 315, 193, 315], [231, 280, 266, 280, 266, 315, 231, 315], [268, 285, 286, 285, 286, 312, 268, 312], [286, 281, 299, 281, 299, 315, 286, 315], [320, 305, 340, 305, 340, 311, 320, 311], [347, 283, 379, 283, 379, 315, 347, 315], [385, 283, 417, 283, 417, 315, 385, 315], [421, 280, 457, 280, 457, 316, 421, 316], [459, 281, 494, 281, 494, 315, 459, 315], [497, 281, 532, 281, 531, 315, 497, 315], [534, 280, 570, 280, 570, 316, 534, 316], [573, 281, 608, 282, 608, 315, 573, 315], [610, 285, 628, 285, 628, 312, 610, 312], [631, 284, 643, 284, 643, 311, 631, 311], [648, 284, 667, 284, 667, 312, 648, 312]]}, {"pos": [41, 327, 289, 327, 289, 367, 41, 367], "score": 0.9990000128746033, "text": "原产国：意大利", "type": "line", "char_pos": [[42, 328, 76, 329, 76, 363, 42, 363], [79, 327, 114, 327, 114, 362, 79, 362], [118, 328, 152, 328, 151, 363, 118, 363], [159, 340, 168, 340, 168, 360, 159, 360], [174, 327, 210, 327, 209, 362, 174, 362], [212, 328, 247, 328, 247, 363, 212, 363], [249, 327, 284, 327, 284, 363, 249, 363]], "id": 5}, {"id": 6, "pos": [41, 373, 476, 373, 476, 413, 41, 413], "score": 0.9990000128746033, "text": "生厂日期：2022年01月12日", "type": "line", "char_pos": [[41, 375, 74, 375, 74, 407, 41, 407], [80, 376, 114, 376, 114, 410, 80, 410], [122, 375, 150, 375, 149, 409, 122, 409], [154, 374, 190, 374, 190, 410, 154, 410], [198, 387, 206, 387, 206, 406, 198, 406], [212, 379, 230, 379, 229, 405, 212, 405], [230, 379, 248, 379, 248, 405, 231, 405], [249, 378, 268, 379, 267, 405, 249, 405], [268, 378, 286, 378, 286, 405, 268, 405], [288, 374, 322, 374, 322, 409, 288, 409], [325, 379, 343, 379, 343, 406, 325, 406], [345, 378, 359, 379, 359, 405, 345, 405], [365, 376, 395, 376, 395, 409, 365, 409], [402, 378, 415, 378, 415, 405, 402, 405], [420, 378, 439, 378, 439, 406, 420, 406], [445, 375, 473, 375, 472, 409, 445, 409]]}, {"char_pos": [[41, 421, 77, 422, 77, 457, 41, 457], [79, 421, 115, 421, 114, 457, 79, 457], [117, 421, 151, 421, 151, 457, 117, 457], [155, 421, 191, 421, 191, 457, 155, 457], [197, 435, 207, 435, 206, 452, 197, 452], [213, 422, 247, 422, 246, 455, 213, 455], [249, 421, 286, 421, 286, 457, 249, 457], [289, 421, 322, 421, 322, 456, 289, 457], [325, 421, 361, 421, 361, 457, 325, 457], [364, 423, 399, 423, 399, 457, 364, 457], [401, 421, 438, 421, 438, 457, 401, 457], [440, 421, 476, 421, 476, 457, 440, 457], [478, 447, 484, 447, 484, 456, 478, 456], [496, 421, 533, 421, 533, 457, 496, 457], [535, 421, 569, 421, 569, 457, 535, 457], [574, 422, 608, 422, 608, 457, 574, 457], [611, 421, 647, 421, 647, 457, 611, 457], [649, 422, 684, 422, 684, 455, 650, 455], [686, 421, 723, 422, 723, 457, 687, 457]], "id": 7, "pos": [40, 420, 727, 420, 727, 461, 40, 461], "score": 0.9990000128746033, "text": "贮存方式：置放闹凉干燥处，避免阳光直酒", "type": "line"}, {"char_pos": [[41, 472, 77, 472, 77, 507, 41, 507], [79, 473, 114, 473, 114, 507, 79, 507], [116, 471, 152, 472, 152, 507, 116, 507], [156, 474, 190, 474, 190, 506, 156, 506], [198, 484, 206, 484, 206, 504, 198, 504], [211, 476, 230, 476, 229, 503, 211, 503], [230, 477, 249, 477, 248, 503, 230, 503], [249, 476, 267, 476, 267, 503, 249, 503], [268, 476, 287, 476, 287, 503, 268, 503], [288, 471, 322, 471, 322, 506, 288, 506], [324, 476, 345, 476, 344, 504, 324, 504], [346, 475, 358, 475, 358, 503, 346, 503], [365, 473, 395, 473, 395, 507, 365, 507], [401, 476, 420, 476, 419, 503, 401, 503], [421, 476, 438, 476, 438, 503, 421, 503], [445, 473, 472, 473, 472, 507, 445, 507]], "id": 8, "pos": [41, 471, 477, 471, 477, 511, 41, 511], "score": 0.9990000128746033, "text": "保质期至：2023年01月23日", "type": "line"}, {"char_pos": [[41, 519, 75, 519, 75, 552, 41, 552], [79, 518, 115, 518, 114, 554, 79, 554], [118, 518, 153, 519, 153, 554, 118, 554], [159, 532, 168, 532, 168, 550, 159, 550], [174, 532, 192, 532, 192, 550, 174, 550], [192, 531, 211, 532, 210, 551, 192, 551], [212, 532, 229, 532, 229, 550, 212, 550]], "id": 9, "pos": [41, 518, 234, 519, 233, 559, 41, 557], "score": 0.9959999918937683, "text": "生产商：xxx", "type": "line"}, {"pos": [41, 565, 347, 565, 347, 605, 41, 605], "score": 0.9980000257492065, "text": "国内总经销商：xxx", "type": "line", "char_pos": [[44, 566, 76, 566, 76, 601, 44, 601], [81, 565, 113, 566, 113, 601, 81, 601], [117, 566, 151, 566, 151, 601, 117, 601], [155, 566, 191, 566, 190, 600, 155, 600], [192, 566, 229, 566, 228, 601, 192, 601], [231, 565, 266, 566, 266, 601, 231, 601], [273, 579, 282, 579, 282, 597, 273, 597], [288, 579, 305, 579, 305, 598, 288, 598], [306, 579, 325, 579, 324, 598, 306, 598], [326, 579, 344, 579, 343, 598, 326, 598]], "id": 10}, {"pos": [41, 612, 195, 615, 195, 654, 41, 652], "score": 0.9950000047683716, "text": "地址：xxx", "type": "line", "char_pos": [[41, 614, 77, 614, 76, 647, 42, 647], [79, 613, 115, 614, 115, 647, 79, 647], [121, 626, 130, 626, 130, 645, 121, 645], [136, 627, 154, 627, 154, 645, 136, 645], [155, 627, 173, 627, 173, 645, 155, 645], [174, 627, 191, 627, 191, 645, 174, 645]], "id": 11}, {"type": "line", "char_pos": [[761, 488, 804, 488, 804, 533, 761, 533], [807, 489, 850, 489, 850, 533, 807, 533], [854, 488, 898, 489, 898, 533, 854, 533], [901, 489, 945, 489, 945, 532, 902, 532], [948, 489, 993, 489, 993, 532, 948, 532]], "id": 12, "pos": [761, 488, 997, 488, 997, 536, 761, 536], "score": 0.9990000128746033, "text": "营养成分表"}, {"type": "line", "char_pos": [[571, 562, 609, 562, 609, 598, 571, 598], [616, 561, 648, 561, 647, 598, 616, 598]], "id": 13, "pos": [569, 560, 653, 560, 653, 601, 569, 601], "score": 0.9990000128746033, "text": "项目"}, {"id": 14, "pos": [823, 559, 950, 561, 949, 603, 822, 601], "score": 0.9990000128746033, "text": "每100g", "type": "line", "char_pos": [[823, 560, 861, 560, 861, 598, 823, 598], [865, 564, 878, 564, 878, 594, 865, 594], [884, 564, 904, 565, 904, 594, 885, 594], [906, 564, 924, 564, 924, 594, 906, 594], [927, 574, 946, 574, 946, 600, 927, 600]]}, {"score": 0.9990000128746033, "text": "NRV%", "type": "line", "char_pos": [[1031, 564, 1052, 564, 1052, 594, 1032, 594], [1053, 565, 1073, 565, 1073, 594, 1053, 594], [1073, 565, 1093, 565, 1093, 594, 1073, 594], [1094, 565, 1105, 565, 1105, 591, 1094, 592]], "id": 15, "pos": [1029, 561, 1119, 561, 1119, 599, 1029, 599]}, {"type": "line", "char_pos": [[571, 607, 610, 607, 610, 647, 572, 647], [612, 608, 652, 608, 652, 646, 612, 646]], "id": 16, "pos": [569, 605, 657, 605, 657, 652, 569, 652], "score": 0.9990000128746033, "text": "能量"}, {"type": "line", "char_pos": [[821, 612, 841, 612, 841, 643, 821, 643], [844, 611, 856, 611, 856, 642, 844, 642], [863, 612, 883, 612, 883, 642, 863, 642], [885, 612, 904, 612, 904, 642, 885, 642], [906, 612, 924, 612, 924, 642, 906, 642]], "id": 17, "pos": [819, 608, 929, 608, 929, 647, 819, 647], "score": 0.996999979019165, "text": "912kJ"}, {"id": 18, "pos": [1033, 611, 1097, 611, 1097, 645, 1033, 645], "score": 0.9990000128746033, "text": "11%", "type": "line", "char_pos": [[1033, 612, 1046, 612, 1046, 642, 1033, 642], [1054, 612, 1067, 612, 1067, 642, 1054, 642], [1073, 613, 1095, 613, 1095, 643, 1073, 643]]}, {"char_pos": [[571, 655, 610, 655, 610, 693, 571, 693], [616, 655, 649, 655, 648, 692, 616, 692], [654, 656, 693, 656, 693, 693, 655, 693]], "id": 19, "pos": [569, 655, 697, 655, 697, 696, 569, 696], "score": 0.9990000128746033, "text": "蛋白质", "type": "line"}, {"char_pos": [[822, 660, 842, 660, 842, 689, 822, 689], [844, 683, 850, 683, 850, 690, 844, 690], [863, 659, 885, 659, 884, 689, 863, 689], [885, 669, 904, 669, 904, 694, 885, 694]], "id": 20, "pos": [821, 656, 909, 659, 908, 698, 819, 695], "score": 0.9990000128746033, "text": "7.4g", "type": "line"}, {"id": 21, "pos": [1033, 657, 1097, 657, 1097, 693, 1033, 693], "score": 0.9990000128746033, "text": "12%", "type": "line", "char_pos": [[1033, 659, 1046, 659, 1045, 689, 1033, 689], [1052, 659, 1072, 659, 1072, 689, 1052, 689], [1073, 660, 1093, 660, 1093, 687, 1073, 687]]}, {"pos": [571, 703, 655, 703, 655, 744, 571, 744], "score": 0.9990000128746033, "text": "脂肪", "type": "line", "char_pos": [[571, 703, 609, 703, 609, 740, 571, 740], [611, 703, 651, 703, 651, 741, 612, 741]], "id": 22}, {"type": "line", "char_pos": [[571, 749, 610, 749, 610, 789, 571, 789], [612, 749, 650, 749, 650, 788, 612, 788], [654, 750, 693, 750, 693, 788, 654, 788], [695, 749, 735, 750, 734, 788, 696, 788], [736, 749, 777, 749, 777, 789, 736, 789]], "id": 23, "pos": [571, 749, 784, 749, 784, 792, 571, 792], "score": 0.9990000128746033, "text": "碳水化合物"}, {"char_pos": [[821, 708, 843, 708, 842, 737, 821, 737], [844, 730, 850, 730, 850, 738, 844, 738], [863, 707, 883, 708, 883, 737, 863, 737], [885, 717, 904, 717, 904, 742, 885, 742]], "id": 24, "pos": [821, 704, 909, 707, 908, 746, 819, 743], "score": 0.9990000128746033, "text": "4.8g", "type": "line"}, {"pos": [819, 751, 930, 754, 929, 794, 818, 791], "score": 0.9990000128746033, "text": "35.8g", "type": "line", "char_pos": [[822, 754, 841, 754, 841, 784, 822, 784], [842, 754, 862, 754, 862, 784, 842, 784], [866, 778, 872, 778, 872, 785, 866, 785], [884, 755, 904, 755, 904, 784, 884, 784], [906, 763, 926, 764, 925, 789, 906, 789]], "id": 25}, {"type": "line", "char_pos": [[1031, 707, 1051, 708, 1051, 737, 1031, 737], [1051, 707, 1068, 707, 1068, 735, 1052, 735]], "id": 26, "pos": [1031, 705, 1077, 705, 1077, 741, 1031, 741], "score": 0.9990000128746033, "text": "8%"}, {"id": 27, "pos": [1033, 752, 1097, 752, 1097, 788, 1033, 788], "score": 0.9990000128746033, "text": "12%", "type": "line", "char_pos": [[1033, 754, 1045, 754, 1045, 784, 1034, 784], [1052, 754, 1072, 754, 1072, 783, 1052, 783], [1073, 755, 1093, 755, 1092, 785, 1073, 784]]}, {"id": 28, "pos": [41, 659, 176, 661, 176, 702, 41, 700], "score": 0.9959999918937683, "text": "电话：xx", "type": "line", "char_pos": [[42, 661, 75, 661, 75, 695, 42, 695], [79, 661, 115, 661, 115, 696, 79, 696], [121, 673, 130, 674, 129, 692, 121, 693], [136, 674, 154, 674, 154, 692, 136, 692], [155, 674, 172, 674, 172, 692, 155, 692]]}, {"char_pos": [[41, 708, 77, 708, 77, 743, 41, 743], [79, 708, 115, 708, 115, 743, 80, 743], [121, 720, 130, 720, 130, 740, 121, 740], [136, 721, 154, 721, 154, 739, 136, 740], [155, 721, 172, 721, 172, 740, 155, 740]], "id": 29, "pos": [41, 708, 176, 708, 176, 747, 41, 747], "score": 0.996999979019165, "text": "传真：xx", "type": "line"}, {"char_pos": [[41, 755, 76, 755, 76, 790, 41, 790], [80, 756, 114, 756, 114, 789, 80, 789], [117, 756, 152, 756, 152, 790, 117, 790], [156, 758, 167, 758, 167, 786, 156, 786], [173, 759, 191, 759, 191, 786, 173, 786], [192, 759, 210, 759, 210, 787, 192, 787], [213, 755, 247, 755, 247, 790, 214, 790]], "id": 30, "pos": [40, 755, 251, 755, 251, 793, 40, 793], "score": 0.9990000128746033, "text": "净重量120克", "type": "line"}], "raw_ocr": [{"angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [1], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "direction": 1, "position": [213, 43, 1000, 42, 1000, 113, 213, 115], "text": "扁桃仁开心果牛奶巧克力", "score": 0.9990000128746033, "type": "text", "char_candidates": [["扁"], ["桃"], ["仁"], ["开"], ["心"], ["果"], ["牛"], ["奶"], ["巧"], ["克"], ["力"]], "char_centers": [[245, 76], [319, 77], [401, 62], [462, 78], [508, 81], [606, 78], [679, 78], [748, 78], [803, 73], [894, 78], [965, 77]], "char_positions": [[213, 43, 279, 43, 278, 110, 213, 109], [285, 44, 353, 45, 353, 110, 285, 110], [379, 57, 423, 57, 423, 68, 380, 68], [429, 45, 495, 46, 495, 111, 429, 110], [500, 67, 516, 67, 516, 96, 500, 96], [574, 46, 639, 46, 639, 110, 574, 110], [646, 45, 713, 45, 712, 111, 646, 111], [715, 45, 781, 45, 781, 112, 715, 112], [787, 50, 820, 50, 819, 97, 788, 97], [861, 44, 927, 44, 927, 112, 861, 112], [934, 44, 997, 44, 997, 110, 934, 110]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 1, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "handwritten": 0}, {"char_positions": [[41, 137, 76, 138, 76, 173, 41, 173], [78, 138, 116, 138, 116, 174, 78, 174], [121, 151, 130, 151, 129, 170, 121, 170], [139, 138, 169, 138, 169, 173, 139, 173], [173, 138, 210, 138, 209, 173, 173, 173], [211, 137, 249, 138, 249, 174, 211, 174], [253, 159, 263, 159, 262, 171, 253, 171], [289, 141, 322, 141, 322, 174, 289, 174], [327, 140, 360, 140, 360, 173, 327, 173], [363, 137, 401, 138, 400, 174, 364, 174], [405, 159, 415, 159, 415, 171, 405, 171], [441, 141, 474, 141, 474, 173, 441, 173], [480, 141, 513, 141, 513, 174, 480, 174], [517, 165, 523, 165, 523, 173, 517, 173], [535, 138, 571, 138, 571, 174, 535, 174], [573, 138, 609, 138, 609, 174, 573, 174], [611, 138, 647, 138, 647, 174, 611, 174], [653, 160, 662, 160, 662, 171, 653, 171], [687, 138, 723, 138, 723, 174, 687, 174], [725, 139, 761, 139, 760, 174, 725, 174], [763, 138, 798, 138, 798, 174, 763, 174], [800, 139, 837, 139, 837, 174, 800, 174], [843, 159, 852, 160, 852, 171, 843, 171], [876, 138, 911, 138, 911, 174, 877, 174], [916, 164, 922, 165, 922, 173, 916, 173], [933, 138, 969, 138, 969, 174, 934, 174], [971, 138, 1008, 139, 1008, 175, 971, 175], [1014, 159, 1024, 159, 1023, 172, 1014, 172], [1047, 139, 1083, 139, 1083, 174, 1047, 174], [1087, 138, 1121, 138, 1121, 173, 1087, 173], [1124, 139, 1159, 139, 1159, 173, 1124, 173]], "direction": 1, "handwritten": 0, "score": 0.9990000128746033, "text": "配料：白砂储、可可快、可可，被物法、脱腻奶粉、乳，清粉、开心果", "type": "text", "char_candidates": [["配"], ["料"], [":", ";"], ["白"], ["砂"], ["储"], ["、", ","], ["可"], ["可"], ["快", "怏"], ["、", ","], ["可"], ["可"], [",", "、"], ["被"], ["物"], ["法"], ["、"], ["脱"], ["腻"], ["奶"], ["粉"], ["、", ","], ["乳"], [",", "、"], ["清"], ["粉"], ["、", ","], ["开"], ["心"], ["果", "里"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9980000257492065, 0.0010000000474974513], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0]], "char_centers": [[58, 155], [97, 156], [125, 160], [154, 155], [191, 155], [230, 155], [257, 165], [305, 157], [343, 156], [382, 155], [410, 165], [457, 157], [496, 157], [520, 169], [553, 156], [591, 156], [629, 156], [657, 165], [705, 156], [742, 156], [780, 156], [818, 156], [847, 165], [893, 156], [919, 168], [951, 156], [989, 156], [1018, 165], [1065, 156], [1104, 155], [1141, 156]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9980000257492065, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "position": [40, 136, 1164, 136, 1164, 177, 40, 177], "angle": 0}, {"char_positions": [[41, 185, 76, 185, 76, 221, 41, 221], [79, 186, 114, 186, 114, 221, 79, 221], [152, 184, 172, 184, 172, 222, 152, 222], [177, 185, 207, 185, 207, 220, 177, 220], [211, 185, 247, 185, 247, 220, 211, 220], [249, 185, 286, 185, 286, 221, 249, 221], [292, 206, 301, 206, 301, 218, 292, 218], [324, 185, 361, 185, 361, 221, 324, 221], [363, 185, 399, 185, 399, 221, 363, 221], [402, 185, 438, 185, 438, 221, 402, 221], [443, 206, 453, 206, 453, 218, 443, 218], [477, 185, 514, 185, 514, 221, 477, 221], [516, 186, 551, 186, 550, 220, 516, 220], [553, 185, 589, 185, 589, 221, 554, 221], [591, 186, 628, 186, 627, 221, 592, 221], [633, 206, 643, 206, 643, 218, 633, 218], [668, 187, 704, 187, 703, 221, 668, 221], [707, 186, 741, 186, 741, 220, 707, 220], [744, 186, 780, 187, 779, 220, 745, 220], [785, 206, 795, 206, 795, 218, 785, 218], [819, 185, 854, 185, 854, 221, 819, 221], [858, 186, 893, 186, 893, 220, 858, 220], [895, 186, 927, 186, 927, 221, 895, 221], [932, 187, 945, 187, 944, 220, 932, 220], [957, 208, 967, 208, 966, 217, 957, 217], [991, 185, 1027, 185, 1026, 221, 991, 221], [1028, 185, 1065, 185, 1064, 221, 1028, 221], [1067, 185, 1102, 185, 1101, 220, 1067, 220], [1104, 185, 1141, 185, 1140, 221, 1104, 221]], "direction": 1, "position": [40, 184, 1145, 184, 1145, 225, 40, 225], "score": 0.9990000128746033, "type": "text", "angle": 0, "char_candidates": [["奶"], ["油"], ["《", "<"], ["白"], ["砂"], ["糖"], ["、", ","], ["横"], ["物"], ["法"], ["、"], ["脱"], ["思"], ["奶"], ["粉"], ["、", ","], ["开"], ["心"], ["果"], ["、", ","], ["乳"], ["清"], ["制"], [")"], ["。", "."], ["房"], ["桃"], ["仁"], ["奶"]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "handwritten": 0, "text": "奶油《白砂糖、横物法、脱思奶粉、开心果、乳清制）。房桃仁奶", "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[58, 203], [96, 203], [162, 203], [192, 202], [229, 202], [267, 203], [296, 212], [342, 203], [381, 203], [420, 203], [448, 212], [495, 203], [533, 203], [571, 203], [609, 203], [638, 212], [685, 204], [724, 203], [762, 203], [790, 212], [836, 203], [875, 203], [911, 203], [938, 203], [961, 212], [1008, 203], [1046, 203], [1084, 202], [1122, 203]]}, {"score": 0.9990000128746033, "char_candidates_scores": [[0.9990000128746033, 0], [0.9990000128746033, 0], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033, 0, 0], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0]], "char_positions": [[42, 233, 76, 233, 76, 268, 42, 268], [95, 232, 115, 232, 115, 268, 95, 269], [120, 232, 150, 232, 150, 267, 120, 267], [154, 232, 191, 232, 190, 267, 155, 267], [192, 232, 229, 232, 229, 267, 192, 267], [234, 253, 244, 253, 244, 265, 234, 265], [267, 232, 304, 232, 304, 268, 267, 268], [306, 232, 342, 232, 342, 268, 306, 268], [345, 232, 381, 232, 381, 268, 345, 268], [386, 253, 396, 253, 396, 265, 386, 265], [421, 232, 457, 232, 457, 268, 421, 268], [459, 232, 494, 232, 494, 268, 459, 268], [496, 233, 533, 233, 532, 269, 496, 269], [539, 254, 548, 254, 548, 265, 539, 265], [573, 233, 608, 233, 608, 267, 574, 267], [610, 232, 646, 232, 646, 268, 610, 268], [649, 232, 684, 233, 684, 267, 650, 267], [690, 254, 700, 254, 700, 265, 690, 265], [725, 232, 759, 233, 758, 268, 725, 268], [764, 259, 769, 259, 769, 267, 764, 267], [782, 232, 817, 232, 817, 268, 782, 268], [819, 232, 856, 232, 855, 268, 819, 268], [857, 234, 869, 234, 869, 267, 857, 267], [880, 255, 890, 255, 890, 264, 880, 265], [915, 232, 950, 232, 950, 268, 915, 268], [952, 232, 988, 232, 988, 268, 952, 268], [990, 232, 1027, 232, 1027, 268, 991, 268], [1029, 233, 1063, 233, 1062, 268, 1029, 268], [1067, 231, 1101, 231, 1101, 268, 1067, 268], [1121, 231, 1141, 232, 1141, 269, 1122, 269]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "direction": 1, "handwritten": 0, "position": [41, 231, 1145, 231, 1145, 272, 41, 272], "angle": 0, "char_candidates": [["油", "由"], ["《", "<"], ["白", "臼"], ["矽"], ["德"], ["、", ","], ["植"], ["物"], ["法"], ["、", ","], ["脱"], ["奶"], ["粉"], ["、", ","], ["腐"], ["桃", "挑"], ["仁"], ["、", ","], ["乳"], [",", "、", "."], ["清", "请"], ["标"], [")"], ["。", "."], ["食"], ["物"], ["添"], ["加"], ["剂"], ["《", "<"]], "char_centers": [[59, 250], [105, 250], [135, 249], [172, 249], [210, 249], [239, 259], [285, 250], [324, 250], [363, 250], [391, 259], [439, 250], [476, 250], [514, 251], [543, 259], [590, 250], [628, 250], [666, 249], [695, 259], [741, 250], [766, 263], [799, 250], [837, 250], [863, 250], [885, 259], [932, 250], [970, 250], [1008, 250], [1045, 250], [1084, 249], [1131, 250]], "text": "油《白矽德、植物法、脱奶粉、腐桃仁、乳，清标）。食物添加剂《", "type": "text"}, {"type": "text", "char_candidates": [["模"], ["测"], ["、", ","], ["食"], ["用"], ["香", "否"], ["F", "下", "E"], [")"], ["。", ".", ","], ["可"], ["可"], ["含"], ["量"], ["不"], ["低"], ["于"], ["3"], ["1"], ["%"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033, 0, 0], [0.9990000128746033], [0.996999979019165, 0.0020000000949949026, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "direction": 1, "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.996999979019165, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "handwritten": 0, "position": [41, 279, 671, 279, 671, 320, 41, 320], "score": 0.9990000128746033, "text": "模测、食用香F）。可可含量不低于31％", "angle": 0, "char_centers": [[59, 298], [96, 298], [125, 307], [172, 298], [209, 298], [248, 297], [277, 298], [292, 298], [330, 308], [363, 299], [401, 299], [439, 298], [476, 298], [514, 298], [552, 298], [590, 298], [619, 298], [637, 297], [657, 298]], "char_positions": [[41, 280, 77, 280, 77, 316, 41, 316], [78, 281, 114, 281, 114, 316, 79, 316], [120, 301, 130, 301, 130, 313, 120, 313], [155, 280, 191, 280, 190, 316, 155, 316], [193, 281, 226, 281, 226, 315, 193, 315], [231, 280, 266, 280, 266, 315, 231, 315], [268, 285, 286, 285, 286, 312, 268, 312], [286, 281, 299, 281, 299, 315, 286, 315], [320, 305, 340, 305, 340, 311, 320, 311], [347, 283, 379, 283, 379, 315, 347, 315], [385, 283, 417, 283, 417, 315, 385, 315], [421, 280, 457, 280, 457, 316, 421, 316], [459, 281, 494, 281, 494, 315, 459, 315], [497, 281, 532, 281, 531, 315, 497, 315], [534, 280, 570, 280, 570, 316, 534, 316], [573, 281, 608, 282, 608, 315, 573, 315], [610, 285, 628, 285, 628, 312, 610, 312], [631, 284, 643, 284, 643, 311, 631, 311], [648, 284, 667, 284, 667, 312, 648, 312]]}, {"char_candidates": [["原"], ["产"], ["国"], [":", ";"], ["意"], ["大"], ["利"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[59, 345], [96, 344], [134, 345], [163, 350], [191, 344], [229, 345], [266, 345]], "direction": 1, "score": 0.9990000128746033, "text": "原产国：意大利", "angle": 0, "char_positions": [[42, 328, 76, 329, 76, 363, 42, 363], [79, 327, 114, 327, 114, 362, 79, 362], [118, 328, 152, 328, 151, 363, 118, 363], [159, 340, 168, 340, 168, 360, 159, 360], [174, 327, 210, 327, 209, 362, 174, 362], [212, 328, 247, 328, 247, 363, 212, 363], [249, 327, 284, 327, 284, 363, 249, 363]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "handwritten": 0, "position": [41, 327, 289, 327, 289, 367, 41, 367], "type": "text"}, {"char_candidates": [["生"], ["厂"], ["日"], ["期"], [":", ";"], ["2"], ["0"], ["2"], ["2"], ["年"], ["0"], ["1"], ["月"], ["1"], ["2"], ["日"]], "char_positions": [[41, 375, 74, 375, 74, 407, 41, 407], [80, 376, 114, 376, 114, 410, 80, 410], [122, 375, 150, 375, 149, 409, 122, 409], [154, 374, 190, 374, 190, 410, 154, 410], [198, 387, 206, 387, 206, 406, 198, 406], [212, 379, 230, 379, 229, 405, 212, 405], [230, 379, 248, 379, 248, 405, 231, 405], [249, 378, 268, 379, 267, 405, 249, 405], [268, 378, 286, 378, 286, 405, 268, 405], [288, 374, 322, 374, 322, 409, 288, 409], [325, 379, 343, 379, 343, 406, 325, 406], [345, 378, 359, 379, 359, 405, 345, 405], [365, 376, 395, 376, 395, 409, 365, 409], [402, 378, 415, 378, 415, 405, 402, 405], [420, 378, 439, 378, 439, 406, 420, 406], [445, 375, 473, 375, 472, 409, 445, 409]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "direction": 1, "score": 0.9990000128746033, "type": "text", "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[57, 391], [97, 393], [135, 392], [172, 392], [202, 396], [220, 392], [239, 392], [258, 391], [277, 391], [305, 391], [334, 392], [352, 391], [380, 392], [408, 391], [429, 392], [458, 392]], "handwritten": 0, "position": [41, 373, 476, 373, 476, 413, 41, 413], "text": "生厂日期：2022年01月12日"}, {"type": "text", "char_candidates": [["贮"], ["存"], ["方"], ["式"], [":", ";"], ["置"], ["放"], ["闹", "闲"], ["凉", "涼", "冻"], ["干", "千", "于"], ["燥"], ["处"], [",", "."], ["避"], ["免"], ["阳", "阴"], ["光"], ["直"], ["酒", "洒"]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "direction": 1, "score": 0.9990000128746033, "text": "贮存方式：置放闹凉干燥处，避免阳光直酒", "position": [40, 420, 727, 420, 727, 461, 40, 461], "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033, 0, 0], [0.9990000128746033, 0, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0]], "char_centers": [[59, 439], [96, 439], [134, 439], [173, 439], [201, 443], [229, 438], [267, 439], [305, 438], [343, 439], [381, 440], [419, 439], [458, 439], [481, 451], [514, 439], [552, 439], [591, 439], [629, 439], [666, 438], [704, 439]], "char_positions": [[41, 421, 77, 422, 77, 457, 41, 457], [79, 421, 115, 421, 114, 457, 79, 457], [117, 421, 151, 421, 151, 457, 117, 457], [155, 421, 191, 421, 191, 457, 155, 457], [197, 435, 207, 435, 206, 452, 197, 452], [213, 422, 247, 422, 246, 455, 213, 455], [249, 421, 286, 421, 286, 457, 249, 457], [289, 421, 322, 421, 322, 456, 289, 457], [325, 421, 361, 421, 361, 457, 325, 457], [364, 423, 399, 423, 399, 457, 364, 457], [401, 421, 438, 421, 438, 457, 401, 457], [440, 421, 476, 421, 476, 457, 440, 457], [478, 447, 484, 447, 484, 456, 478, 456], [496, 421, 533, 421, 533, 457, 496, 457], [535, 421, 569, 421, 569, 457, 535, 457], [574, 422, 608, 422, 608, 457, 574, 457], [611, 421, 647, 421, 647, 457, 611, 457], [649, 422, 684, 422, 684, 455, 650, 455], [686, 421, 723, 422, 723, 457, 687, 457]], "handwritten": 0}, {"direction": 1, "handwritten": 0, "position": [41, 471, 477, 471, 477, 511, 41, 511], "score": 0.9990000128746033, "char_candidates": [["保"], ["质"], ["期"], ["至", "止"], [":", ";", " "], ["2"], ["0"], ["2"], ["3"], ["年"], ["0"], ["1"], ["月"], ["2"], ["3", "8"], ["日"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033, 0, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033]], "char_centers": [[59, 489], [96, 490], [134, 489], [173, 490], [202, 494], [220, 489], [239, 490], [258, 489], [277, 489], [305, 488], [334, 490], [352, 489], [380, 490], [410, 489], [429, 489], [458, 490]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "text": "保质期至：2023年01月23日", "angle": 0, "char_positions": [[41, 472, 77, 472, 77, 507, 41, 507], [79, 473, 114, 473, 114, 507, 79, 507], [116, 471, 152, 472, 152, 507, 116, 507], [156, 474, 190, 474, 190, 506, 156, 506], [198, 484, 206, 484, 206, 504, 198, 504], [211, 476, 230, 476, 229, 503, 211, 503], [230, 477, 249, 477, 248, 503, 230, 503], [249, 476, 267, 476, 267, 503, 249, 503], [268, 476, 287, 476, 287, 503, 268, 503], [288, 471, 322, 471, 322, 506, 288, 506], [324, 476, 345, 476, 344, 504, 324, 504], [346, 475, 358, 475, 358, 503, 346, 503], [365, 473, 395, 473, 395, 507, 365, 507], [401, 476, 420, 476, 419, 503, 401, 503], [421, 476, 438, 476, 438, 503, 421, 503], [445, 473, 472, 473, 472, 507, 445, 507]], "type": "text"}, {"char_candidates": [["营"], ["养"], ["成"], ["分"], ["表"]], "char_centers": [[782, 510], [828, 511], [876, 510], [923, 510], [970, 510]], "char_positions": [[761, 488, 804, 488, 804, 533, 761, 533], [807, 489, 850, 489, 850, 533, 807, 533], [854, 488, 898, 489, 898, 533, 854, 533], [901, 489, 945, 489, 945, 532, 902, 532], [948, 489, 993, 489, 993, 532, 948, 532]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "position": [761, 488, 997, 488, 997, 536, 761, 536], "text": "营养成分表", "type": "text", "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "direction": 1, "handwritten": 0, "score": 0.9990000128746033}, {"char_candidates": [["生"], ["产"], ["商", "高"], [":", ";"], ["x", "X"], ["x", "X"], ["x", "X"]], "char_centers": [[58, 535], [96, 536], [135, 536], [163, 541], [183, 541], [201, 541], [220, 541]], "score": 0.9959999918937683, "text": "生产商：xxx", "type": "text", "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033, 0], [0.9909999966621399, 0.00800000037997961], [0.9900000095367432, 0.008999999612569809], [0.9919999837875366, 0.007000000216066837]], "char_positions": [[41, 519, 75, 519, 75, 552, 41, 552], [79, 518, 115, 518, 114, 554, 79, 554], [118, 518, 153, 519, 153, 554, 118, 554], [159, 532, 168, 532, 168, 550, 159, 550], [174, 532, 192, 532, 192, 550, 174, 550], [192, 531, 211, 532, 210, 551, 192, 551], [212, 532, 229, 532, 229, 550, 212, 550]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9909999966621399, 0.9900000095367432, 0.9919999837875366], "direction": 1, "handwritten": 0, "position": [41, 518, 234, 519, 233, 559, 41, 557]}, {"char_positions": [[44, 566, 76, 566, 76, 601, 44, 601], [81, 565, 113, 566, 113, 601, 81, 601], [117, 566, 151, 566, 151, 601, 117, 601], [155, 566, 191, 566, 190, 600, 155, 600], [192, 566, 229, 566, 228, 601, 192, 601], [231, 565, 266, 566, 266, 601, 231, 601], [273, 579, 282, 579, 282, 597, 273, 597], [288, 579, 305, 579, 305, 598, 288, 598], [306, 579, 325, 579, 324, 598, 306, 598], [326, 579, 344, 579, 343, 598, 326, 598]], "handwritten": 0, "position": [41, 565, 347, 565, 347, 605, 41, 605], "score": 0.9980000257492065, "text": "国内总经销商：xxx", "angle": 0, "char_candidates": [["国"], ["内"], ["总"], ["经"], ["销", "消"], ["商"], [":", ";"], ["x", "X"], ["x", "X"], ["x", "X"]], "char_centers": [[60, 583], [97, 583], [134, 583], [172, 583], [210, 583], [248, 583], [277, 588], [296, 588], [315, 588], [334, 588]], "type": "text", "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033, 0], [0.9959999918937683, 0.003000000026077032], [0.9919999837875366, 0.007000000216066837], [0.9950000047683716, 0.004000000189989805]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9959999918937683, 0.9919999837875366, 0.9950000047683716], "direction": 1}, {"angle": 0, "char_centers": [[590, 580], [631, 579]], "direction": 1, "score": 0.9990000128746033, "text": "项目", "type": "text", "char_candidates": [["项"], ["目"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033]], "char_positions": [[571, 562, 609, 562, 609, 598, 571, 598], [616, 561, 648, 561, 647, 598, 616, 598]], "char_scores": [0.9990000128746033, 0.9990000128746033], "handwritten": 0, "position": [569, 560, 653, 560, 653, 601, 569, 601]}, {"type": "text", "char_candidates_scores": [[0.9990000128746033, 0, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0]], "handwritten": 0, "char_centers": [[842, 579], [871, 579], [894, 579], [915, 579], [936, 587]], "char_positions": [[823, 560, 861, 560, 861, 598, 823, 598], [865, 564, 878, 564, 878, 594, 865, 594], [884, 564, 904, 565, 904, 594, 885, 594], [906, 564, 924, 564, 924, 594, 906, 594], [927, 574, 946, 574, 946, 600, 927, 600]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "direction": 1, "position": [823, 559, 950, 561, 949, 603, 822, 601], "score": 0.9990000128746033, "angle": 0, "char_candidates": [["每", "毎", "海"], ["1"], ["0"], ["0"], ["g", "8"]], "text": "每100g"}, {"char_candidates_scores": [[0.9990000128746033, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0, 0, 0]], "char_positions": [[1031, 564, 1052, 564, 1052, 594, 1032, 594], [1053, 565, 1073, 565, 1073, 594, 1053, 594], [1073, 565, 1093, 565, 1093, 594, 1073, 594], [1094, 565, 1105, 565, 1105, 591, 1094, 592]], "direction": 1, "type": "text", "angle": 0, "char_centers": [[1041, 579], [1063, 579], [1083, 579], [1099, 578]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "handwritten": 0, "position": [1029, 561, 1119, 561, 1119, 599, 1029, 599], "score": 0.9990000128746033, "text": "NRV%", "char_candidates": [["N", "A"], ["R"], ["V"], ["%", "9", "®", "8"]]}, {"char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9929999709129333, 0.9879999756813049, 0.9900000095367432], "direction": 1, "handwritten": 0, "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9929999709129333, 0.006000000052154064], [0.9879999756813049, 0.010999999940395355], [0.9900000095367432, 0.008999999612569809]], "char_centers": [[59, 630], [97, 630], [125, 635], [145, 636], [164, 636], [182, 636]], "char_positions": [[41, 614, 77, 614, 76, 647, 42, 647], [79, 613, 115, 614, 115, 647, 79, 647], [121, 626, 130, 626, 130, 645, 121, 645], [136, 627, 154, 627, 154, 645, 136, 645], [155, 627, 173, 627, 173, 645, 155, 645], [174, 627, 191, 627, 191, 645, 174, 645]], "position": [41, 612, 195, 615, 195, 654, 41, 652], "score": 0.9950000047683716, "text": "地址：xxx", "type": "text", "char_candidates": [["地"], ["址"], [":", ";"], ["x", "X"], ["x", "X"], ["x", "X"]]}, {"position": [569, 605, 657, 605, 657, 652, 569, 652], "type": "text", "char_candidates": [["能"], ["量"]], "handwritten": 0, "char_centers": [[590, 627], [632, 627]], "char_positions": [[571, 607, 610, 607, 610, 647, 572, 647], [612, 608, 652, 608, 652, 646, 612, 646]], "char_scores": [0.9990000128746033, 0.9990000128746033], "direction": 1, "score": 0.9990000128746033, "text": "能量", "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033]]}, {"char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9869999885559082, 0.9990000128746033], "handwritten": 0, "position": [819, 608, 929, 608, 929, 647, 819, 647], "text": "912kJ", "char_candidates": [["9"], ["1"], ["2"], ["k", "K"], ["J", "j", "d", "U"]], "char_centers": [[831, 627], [850, 626], [873, 627], [894, 627], [915, 627]], "char_positions": [[821, 612, 841, 612, 841, 643, 821, 643], [844, 611, 856, 611, 856, 642, 844, 642], [863, 612, 883, 612, 883, 642, 863, 642], [885, 612, 904, 612, 904, 642, 885, 642], [906, 612, 924, 612, 924, 642, 906, 642]], "direction": 1, "score": 0.996999979019165, "type": "text", "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9869999885559082, 0.012000000104308128], [0.9990000128746033, 0, 0, 0]]}, {"score": 0.9990000128746033, "text": "11%", "angle": 0, "char_positions": [[1033, 612, 1046, 612, 1046, 642, 1033, 642], [1054, 612, 1067, 612, 1067, 642, 1054, 642], [1073, 613, 1095, 613, 1095, 643, 1073, 643]], "direction": 1, "handwritten": 0, "position": [1033, 611, 1097, 611, 1097, 645, 1033, 645], "char_candidates": [["1"], ["1"], ["%"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[1039, 627], [1060, 627], [1084, 628]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "type": "text"}, {"type": "text", "angle": 0, "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9940000176429749, 0.9900000095367432], "handwritten": 0, "char_positions": [[42, 661, 75, 661, 75, 695, 42, 695], [79, 661, 115, 661, 115, 696, 79, 696], [121, 673, 130, 674, 129, 692, 121, 693], [136, 674, 154, 674, 154, 692, 136, 692], [155, 674, 172, 674, 172, 692, 155, 692]], "direction": 1, "position": [41, 659, 176, 661, 176, 702, 41, 700], "score": 0.9959999918937683, "text": "电话：xx", "char_candidates": [["电"], ["话"], [":", ";"], ["x", "X"], ["x", "X"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9940000176429749, 0.004999999888241291], [0.9900000095367432, 0.008999999612569809]], "char_centers": [[58, 678], [97, 678], [125, 683], [145, 683], [163, 683]]}, {"score": 0.9990000128746033, "text": "蛋白质", "type": "text", "angle": 0, "char_positions": [[571, 655, 610, 655, 610, 693, 571, 693], [616, 655, 649, 655, 648, 692, 616, 692], [654, 656, 693, 656, 693, 693, 655, 693]], "direction": 1, "position": [569, 655, 697, 655, 697, 696, 569, 696], "handwritten": 0, "char_candidates": [["蛋"], ["白"], ["质"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[590, 674], [632, 673], [673, 674]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033]}, {"direction": 1, "handwritten": 0, "position": [821, 656, 909, 659, 908, 698, 819, 695], "score": 0.9990000128746033, "type": "text", "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0]], "char_centers": [[832, 674], [847, 686], [873, 674], [894, 681]], "char_positions": [[822, 660, 842, 660, 842, 689, 822, 689], [844, 683, 850, 683, 850, 690, 844, 690], [863, 659, 885, 659, 884, 689, 863, 689], [885, 669, 904, 669, 904, 694, 885, 694]], "text": "7.4g", "angle": 0, "char_candidates": [["7"], ["."], ["4"], ["g", "8"]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033]}, {"angle": 0, "handwritten": 0, "position": [1033, 657, 1097, 657, 1097, 693, 1033, 693], "score": 0.9990000128746033, "direction": 1, "text": "12%", "type": "text", "char_candidates": [["1"], ["2"], ["%"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[1039, 674], [1062, 674], [1083, 673]], "char_positions": [[1033, 659, 1046, 659, 1045, 689, 1033, 689], [1052, 659, 1072, 659, 1072, 689, 1052, 689], [1073, 660, 1093, 660, 1093, 687, 1073, 687]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033]}, {"char_centers": [[59, 725], [97, 725], [125, 730], [145, 730], [163, 730]], "handwritten": 0, "position": [41, 708, 176, 708, 176, 747, 41, 747], "type": "text", "char_candidates": [["传"], ["真"], [":", ";"], ["x", "X"], ["x", "X"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0], [0.9950000047683716, 0.004000000189989805], [0.9940000176429749, 0.004999999888241291]], "char_positions": [[41, 708, 77, 708, 77, 743, 41, 743], [79, 708, 115, 708, 115, 743, 80, 743], [121, 720, 130, 720, 130, 740, 121, 740], [136, 721, 154, 721, 154, 739, 136, 740], [155, 721, 172, 721, 172, 740, 155, 740]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9950000047683716, 0.9940000176429749], "direction": 1, "score": 0.996999979019165, "text": "传真：xx", "angle": 0}, {"score": 0.9990000128746033, "type": "text", "angle": 0, "char_centers": [[590, 721], [631, 722]], "char_positions": [[571, 703, 609, 703, 609, 740, 571, 740], [611, 703, 651, 703, 651, 741, 612, 741]], "char_scores": [0.9990000128746033, 0.9990000128746033], "handwritten": 0, "position": [571, 703, 655, 703, 655, 744, 571, 744], "char_candidates": [["脂", "酯", "指"], ["肪"]], "char_candidates_scores": [[0.9990000128746033, 0, 0], [0.9990000128746033]], "direction": 1, "text": "脂肪"}, {"char_positions": [[821, 708, 843, 708, 842, 737, 821, 737], [844, 730, 850, 730, 850, 738, 844, 738], [863, 707, 883, 708, 883, 737, 863, 737], [885, 717, 904, 717, 904, 742, 885, 742]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "direction": 1, "handwritten": 0, "position": [821, 704, 909, 707, 908, 746, 819, 743], "angle": 0, "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0]], "char_centers": [[831, 722], [847, 734], [873, 722], [894, 729]], "type": "text", "char_candidates": [["4"], ["."], ["8"], ["g", "8"]], "score": 0.9990000128746033, "text": "4.8g"}, {"angle": 0, "char_candidates": [["8", "%", "9", "0"], ["%", "8", "6"]], "char_candidates_scores": [[0.9990000128746033, 0, 0, 0], [0.9990000128746033, 0, 0]], "char_centers": [[1041, 722], [1059, 721]], "char_positions": [[1031, 707, 1051, 708, 1051, 737, 1031, 737], [1051, 707, 1068, 707, 1068, 735, 1052, 735]], "char_scores": [0.9990000128746033, 0.9990000128746033], "type": "text", "direction": 1, "handwritten": 0, "position": [1031, 705, 1077, 705, 1077, 741, 1031, 741], "score": 0.9990000128746033, "text": "8%"}, {"char_candidates_scores": [[0.9990000128746033, 0, 0], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "direction": 1, "handwritten": 0, "score": 0.9990000128746033, "type": "text", "angle": 0, "char_candidates": [["净", "浄", "凈"], ["重"], ["量"], ["1"], ["2"], ["0"], ["克"]], "char_centers": [[58, 772], [97, 772], [134, 773], [161, 772], [182, 772], [201, 773], [230, 772]], "char_positions": [[41, 755, 76, 755, 76, 790, 41, 790], [80, 756, 114, 756, 114, 789, 80, 789], [117, 756, 152, 756, 152, 790, 117, 790], [156, 758, 167, 758, 167, 786, 156, 786], [173, 759, 191, 759, 191, 786, 173, 786], [192, 759, 210, 759, 210, 787, 192, 787], [213, 755, 247, 755, 247, 790, 214, 790]], "position": [40, 755, 251, 755, 251, 793, 40, 793], "text": "净重量120克"}, {"char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "score": 0.9990000128746033, "text": "碳水化合物", "char_positions": [[571, 749, 610, 749, 610, 789, 571, 789], [612, 749, 650, 749, 650, 788, 612, 788], [654, 750, 693, 750, 693, 788, 654, 788], [695, 749, 735, 750, 734, 788, 696, 788], [736, 749, 777, 749, 777, 789, 736, 789]], "direction": 1, "handwritten": 0, "position": [571, 749, 784, 749, 784, 792, 571, 792], "angle": 0, "char_candidates": [["碳"], ["水"], ["化"], ["合"], ["物"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[590, 769], [631, 768], [673, 769], [715, 768], [756, 769]], "type": "text"}, {"angle": 0, "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "position": [819, 751, 930, 754, 929, 794, 818, 791], "type": "text", "char_candidates": [["3"], ["5"], ["."], ["8"], ["g", "8"]], "char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033], [0.9990000128746033, 0]], "char_centers": [[831, 769], [852, 769], [869, 781], [894, 769], [915, 776]], "char_positions": [[822, 754, 841, 754, 841, 784, 822, 784], [842, 754, 862, 754, 862, 784, 842, 784], [866, 778, 872, 778, 872, 785, 866, 785], [884, 755, 904, 755, 904, 784, 884, 784], [906, 763, 926, 764, 925, 789, 906, 789]], "direction": 1, "handwritten": 0, "score": 0.9990000128746033, "text": "35.8g"}, {"char_candidates_scores": [[0.9990000128746033], [0.9990000128746033], [0.9990000128746033]], "char_centers": [[1039, 769], [1062, 768], [1082, 769]], "char_scores": [0.9990000128746033, 0.9990000128746033, 0.9990000128746033], "handwritten": 0, "score": 0.9990000128746033, "angle": 0, "char_candidates": [["1"], ["2"], ["%"]], "position": [1033, 752, 1097, 752, 1097, 788, 1033, 788], "text": "12%", "type": "text", "char_positions": [[1033, 754, 1045, 754, 1045, 784, 1034, 784], [1052, 754, 1072, 754, 1072, 783, 1052, 783], [1073, 755, 1093, 755, 1092, 785, 1073, 784]], "direction": 1}], "status": "Success", "width": 1200, "page_id": 1, "structured": [{"content": [0], "id": 0, "outline_level": -1, "pos": [213, 43, 1000, 42, 999, 111, 212, 113], "sub_type": "text_title", "text": "扁桃仁开心果牛奶巧克力", "type": "textblock"}, {"sub_type": "text", "text": "配料：白砂储、可可快、可可，被物法、脱腻奶粉、乳，清粉、开心果奶油《白砂糖、横物法、脱思奶粉、开心果、乳清制）。房桃仁奶油《白矽德、植物法、脱奶粉、腐桃仁、乳，清标）。食物添加剂《模测、食用香F）。可可含量不低于31％", "type": "textblock", "content": [1, 2, 3, 4], "id": 1, "outline_level": -1, "pos": [40, 137, 1164, 137, 1164, 316, 40, 316]}, {"content": [5], "id": 2, "outline_level": -1, "pos": [41, 327, 289, 327, 289, 363, 41, 363], "sub_type": "text", "text": "原产国：意大利", "type": "textblock"}, {"outline_level": -1, "pos": [41, 374, 476, 374, 476, 410, 41, 410], "sub_type": "text", "text": "生厂日期：2022年01月12日", "type": "textblock", "content": [6], "id": 3}, {"type": "textblock", "content": [7], "id": 4, "outline_level": -1, "pos": [40, 421, 727, 421, 727, 457, 40, 457], "sub_type": "text", "text": "贮存方式：置放闹凉干燥处，避免阳光直酒"}, {"pos": [41, 471, 477, 471, 477, 507, 41, 507], "sub_type": "text", "text": "保质期至：2023年01月23日", "type": "textblock", "content": [8], "id": 5, "outline_level": -1}, {"sub_type": "text", "text": "生产商：xxx", "type": "textblock", "content": [9], "id": 6, "outline_level": -1, "pos": [41, 518, 234, 519, 233, 559, 41, 557]}, {"sub_type": "text", "text": "国内总经销商：xxx", "type": "textblock", "content": [10], "id": 7, "outline_level": -1, "pos": [41, 565, 347, 565, 347, 601, 41, 601]}, {"pos": [40, 612, 194, 615, 195, 648, 41, 646], "sub_type": "text", "text": "地址：xxx", "type": "textblock", "content": [11], "id": 8, "outline_level": -1}, {"id": 9, "rows_height": [78, 53, 47, 45, 47, 59], "text": "<table border=\"1\" ><tr>\n<td colspan=\"3\" rowspan=\"1\">营养成分表</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">项目</td>\n<td colspan=\"1\" rowspan=\"1\">每100g </td>\n<td colspan=\"1\" rowspan=\"1\">NRV%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">能量</td>\n<td colspan=\"1\" rowspan=\"1\">912kJ </td>\n<td colspan=\"1\" rowspan=\"1\">11%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">蛋白质</td>\n<td colspan=\"1\" rowspan=\"1\">7.4g </td>\n<td colspan=\"1\" rowspan=\"1\">12%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"2\">脂肪<br>碳水化合物</td>\n<td colspan=\"1\" rowspan=\"2\">4.8g<br>35.8g </td>\n<td colspan=\"1\" rowspan=\"1\">8%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">12%</td>\n</tr></table>", "type": "table", "cols": 3, "columns_width": [275, 191, 162], "parse_type": "Ocr", "pos": [521, 465, 1159, 470, 1158, 807, 520, 802], "rows": 6, "sub_type": "bordered", "cells": [{"content": [{"content": [12], "pos": [522, 467, 1151, 467, 1151, 546, 522, 546], "type": "textblock"}], "pos": [522, 467, 1151, 467, 1151, 546, 522, 546], "row": 0, "row_span": 1, "col": 0, "col_span": 3}, {"row": 1, "row_span": 1, "col": 0, "col_span": 1, "content": [{"content": [13], "pos": [522, 546, 798, 546, 798, 599, 522, 599], "type": "textblock"}], "pos": [522, 546, 798, 546, 798, 599, 522, 599]}, {"col": 1, "col_span": 1, "content": [{"content": [14], "pos": [798, 546, 988, 546, 988, 599, 798, 599], "type": "textblock"}], "pos": [798, 546, 988, 546, 988, 599, 798, 599], "row": 1, "row_span": 1}, {"col": 2, "col_span": 1, "content": [{"content": [15], "pos": [988, 546, 1151, 546, 1151, 599, 988, 599], "type": "textblock"}], "pos": [988, 546, 1151, 546, 1151, 599, 988, 599], "row": 1, "row_span": 1}, {"col": 0, "col_span": 1, "content": [{"content": [16], "pos": [522, 599, 798, 599, 798, 646, 522, 646], "type": "textblock"}], "pos": [522, 599, 798, 599, 798, 646, 522, 646], "row": 2, "row_span": 1}, {"row": 2, "row_span": 1, "col": 1, "col_span": 1, "content": [{"content": [17], "pos": [798, 599, 988, 599, 988, 646, 798, 646], "type": "textblock"}], "pos": [798, 599, 988, 599, 988, 646, 798, 646]}, {"pos": [988, 599, 1151, 599, 1151, 646, 988, 646], "row": 2, "row_span": 1, "col": 2, "col_span": 1, "content": [{"content": [18], "pos": [988, 599, 1151, 599, 1151, 646, 988, 646], "type": "textblock"}]}, {"col": 0, "col_span": 1, "content": [{"pos": [522, 646, 798, 646, 798, 692, 522, 692], "type": "textblock", "content": [19]}], "pos": [522, 646, 798, 646, 798, 692, 522, 692], "row": 3, "row_span": 1}, {"col": 1, "col_span": 1, "content": [{"content": [20], "pos": [798, 646, 988, 646, 988, 692, 798, 692], "type": "textblock"}], "pos": [798, 646, 988, 646, 988, 692, 798, 692], "row": 3, "row_span": 1}, {"col": 2, "col_span": 1, "content": [{"content": [21], "pos": [988, 646, 1151, 646, 1151, 692, 988, 692], "type": "textblock"}], "pos": [988, 646, 1151, 646, 1151, 692, 988, 692], "row": 3, "row_span": 1}, {"col": 0, "col_span": 1, "content": [{"content": [22, 23], "pos": [522, 692, 798, 692, 798, 799, 522, 799], "type": "textblock"}], "pos": [522, 692, 798, 692, 798, 799, 522, 799], "row": 4, "row_span": 2}, {"row_span": 2, "col": 1, "col_span": 1, "content": [{"content": [24, 25], "pos": [798, 692, 988, 692, 988, 799, 798, 799], "type": "textblock"}], "pos": [798, 692, 988, 692, 988, 799, 798, 799], "row": 4}, {"col": 2, "col_span": 1, "content": [{"content": [26], "pos": [988, 692, 1151, 692, 1151, 739, 988, 739], "type": "textblock"}], "pos": [988, 692, 1151, 692, 1151, 739, 988, 739], "row": 4, "row_span": 1}, {"col": 2, "col_span": 1, "content": [{"pos": [988, 739, 1151, 739, 1151, 799, 988, 799], "type": "textblock", "content": [27]}], "pos": [988, 739, 1151, 739, 1151, 799, 988, 799], "row": 5, "row_span": 1}], "outline_level": -1}, {"sub_type": "text", "text": "电话：xx", "type": "textblock", "content": [28], "id": 10, "outline_level": -1, "pos": [40, 659, 175, 661, 176, 697, 41, 695]}, {"content": [29], "id": 11, "outline_level": -1, "pos": [41, 708, 176, 708, 176, 743, 41, 743], "sub_type": "text", "text": "传真：xx", "type": "textblock"}, {"text": "净重量120克", "type": "textblock", "content": [30], "id": 12, "outline_level": -1, "pos": [40, 755, 251, 755, 251, 790, 40, 790], "sub_type": "text"}], "angle": 0, "durations": 443.8504638671875, "height": 840, "image_id": ""}], "success_count": 1, "total_count": 1, "total_page_number": 1, "valid_page_number": 1}, "code": 200, "image_process": [], "message": "Success", "version": "3.17.12", "duration": 458, "x_request_id": "3dc0765f376dbf86492545a6771584c8"}