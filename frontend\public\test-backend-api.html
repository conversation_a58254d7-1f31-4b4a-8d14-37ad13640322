<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后端API测试 - 图片识别</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }

        .upload-section.dragover {
            border-color: #00f2fe;
            background: #e6f7ff;
            transform: scale(1.02);
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .api-config {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .api-config h3 {
            color: #856404;
            margin-bottom: 10px;
        }

        .api-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px 0;
        }

        .preview-section {
            display: none;
            margin: 20px 0;
        }

        .image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .results-section {
            display: none;
            margin-top: 30px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #4facfe;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .element-type {
            background: #4facfe;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .element-content {
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px 0;
            color: #333;
        }

        .element-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .detail-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }

        .detail-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
        }

        .detail-value {
            color: #333;
            margin-top: 5px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .toggle-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }

        .toggle-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 后端API测试工具</h1>
            <p>测试图片识别后端接口的功能和性能</p>
        </div>

        <div class="main-content">
            <!-- API配置区域 -->
            <div class="api-config">
                <h3>🔧 API配置</h3>
                <input type="text" id="apiUrl" class="api-input" 
                       value="http://localhost:3001/api/recognize" 
                       placeholder="后端API地址">
                <small>确保后端服务器正在运行在指定地址</small>
            </div>

            <!-- 上传区域 -->
            <div class="upload-section" id="uploadSection">
                <h3>📤 选择或拖拽图片文件</h3>
                <p>支持 JPG, PNG, GIF, BMP 等格式</p>
                <input type="file" id="fileInput" class="file-input" accept="image/*">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择图片文件
                </button>
                <button class="upload-btn" onclick="loadTestImage()">
                    使用测试图片
                </button>
            </div>

            <!-- 图片预览 -->
            <div class="preview-section" id="previewSection">
                <h3>🖼️ 图片预览</h3>
                <img id="imagePreview" class="image-preview" alt="图片预览">
                <div id="imageInfo" class="result-card"></div>
                <button class="upload-btn" onclick="recognizeImage()">
                    🚀 开始识别
                </button>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loadingSection">
                <div class="spinner"></div>
                <p>正在识别图片，请稍候...</p>
            </div>

            <!-- 结果展示 -->
            <div class="results-section" id="resultsSection">
                <h3>📊 识别结果</h3>
                <div id="statsGrid" class="stats-grid"></div>
                <div id="elementsContainer"></div>
                
                <button class="toggle-btn" onclick="toggleJsonViewer()">
                    显示/隐藏 JSON 数据
                </button>
                <div id="jsonViewer" class="json-viewer" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let currentFile = null;
        let recognitionResult = null;

        // 文件拖拽处理
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        // 处理文件选择
        function handleFileSelect(file) {
            if (!file.type.startsWith('image/')) {
                showError('请选择图片文件！');
                return;
            }

            currentFile = file;
            
            // 显示图片预览
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.getElementById('imagePreview');
                preview.src = e.target.result;
                
                // 显示图片信息
                showImageInfo(file);
                
                // 显示预览区域
                document.getElementById('previewSection').style.display = 'block';
                
                // 隐藏结果区域
                document.getElementById('resultsSection').style.display = 'none';
            };
            reader.readAsDataURL(file);
        }

        // 显示图片信息
        function showImageInfo(file) {
            const img = new Image();
            img.onload = () => {
                const infoHtml = `
                    <div class="result-header">
                        <h4>📷 图片信息</h4>
                    </div>
                    <div class="element-details">
                        <div class="detail-item">
                            <div class="detail-label">文件名</div>
                            <div class="detail-value">${file.name}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">文件大小</div>
                            <div class="detail-value">${(file.size / 1024).toFixed(2)} KB</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">图片尺寸</div>
                            <div class="detail-value">${img.width} × ${img.height} px</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">文件类型</div>
                            <div class="detail-value">${file.type}</div>
                        </div>
                    </div>
                `;
                document.getElementById('imageInfo').innerHTML = infoHtml;
            };
            img.src = URL.createObjectURL(file);
        }

        // 加载测试图片
        async function loadTestImage() {
            try {
                // 创建一个简单的测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 400;
                canvas.height = 300;
                const ctx = canvas.getContext('2d');
                
                // 绘制背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, 400, 300);
                
                // 绘制文本
                ctx.fillStyle = '#333333';
                ctx.font = '32px Arial';
                ctx.fillText('Hello World!', 50, 100);
                ctx.fillText('Test Image', 50, 150);
                ctx.fillText('测试图片', 50, 200);
                
                // 转换为Blob
                canvas.toBlob((blob) => {
                    const file = new File([blob], 'test-image.png', { type: 'image/png' });
                    handleFileSelect(file);
                }, 'image/png');
                
            } catch (error) {
                showError('创建测试图片失败: ' + error.message);
            }
        }

        // 识别图片
        async function recognizeImage() {
            if (!currentFile) {
                showError('请先选择图片文件！');
                return;
            }

            const apiUrl = document.getElementById('apiUrl').value;
            if (!apiUrl) {
                showError('请输入API地址！');
                return;
            }

            // 显示加载状态
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';

            try {
                const formData = new FormData();
                formData.append('image', currentFile);

                const startTime = Date.now();
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    body: formData
                });

                const endTime = Date.now();
                const duration = endTime - startTime;

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                // 隐藏加载状态
                document.getElementById('loadingSection').style.display = 'none';
                
                if (result.success) {
                    recognitionResult = result;
                    showResults(result, duration);
                    showSuccess(`识别成功！耗时: ${duration}ms`);
                } else {
                    showError('识别失败: ' + (result.error?.message || '未知错误'));
                }

            } catch (error) {
                document.getElementById('loadingSection').style.display = 'none';
                showError('API调用失败: ' + error.message);
            }
        }

        // 显示识别结果
        function showResults(result, duration) {
            const { imageInfo, elements } = result.data;
            
            // 显示统计信息
            showStats(imageInfo, elements, duration);
            
            // 显示元素详情
            showElements(elements);
            
            // 显示JSON数据
            document.getElementById('jsonViewer').textContent = JSON.stringify(result, null, 2);
            
            // 显示结果区域
            document.getElementById('resultsSection').style.display = 'block';
        }

        // 显示统计信息
        function showStats(imageInfo, elements, duration) {
            const typeCounts = {};
            const elementTypeCounts = {};

            elements.forEach(el => {
                const type = getElementTypeName(el.elementType);
                typeCounts[type] = (typeCounts[type] || 0) + 1;
                elementTypeCounts[el.elementType] = (elementTypeCounts[el.elementType] || 0) + 1;
            });

            // 基础统计卡片
            let statsHtml = `
                <div class="stat-card">
                    <div class="stat-number">${elements.length}</div>
                    <div class="stat-label">识别元素</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${duration}ms</div>
                    <div class="stat-label">识别耗时</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${imageInfo.width}×${imageInfo.height}</div>
                    <div class="stat-label">图片尺寸</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(typeCounts).length}</div>
                    <div class="stat-label">元素类型</div>
                </div>
            `;

            // 添加详细的元素类型统计
            Object.keys(elementTypeCounts).forEach(elementType => {
                const typeName = getElementTypeName(elementType);
                const count = elementTypeCounts[elementType];
                statsHtml += `
                    <div class="stat-card">
                        <div class="stat-number">${count}</div>
                        <div class="stat-label">${typeName} (${elementType})</div>
                    </div>
                `;
            });

            document.getElementById('statsGrid').innerHTML = statsHtml;
        }

        // 显示元素详情
        function showElements(elements) {
            const container = document.getElementById('elementsContainer');
            
            if (elements.length === 0) {
                container.innerHTML = '<p>未识别到任何元素</p>';
                return;
            }

            const elementsHtml = elements.map((element, index) => {
                const typeName = getElementTypeName(element.elementType);
                
                let detailsHtml = `

                    <div class="detail-item">
                        <div class="detail-label">位置</div>
                        <div class="detail-value">(${element.x}, ${element.y})</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">尺寸</div>
                        <div class="detail-value">${element.width} × ${element.height} px</div>
                    </div>
                `;

                if (element.elementType === '1') { // 文本
                    detailsHtml += `
                        <div class="detail-item">
                            <div class="detail-label">字符宽度</div>
                            <div class="detail-value">${element.charWidth} px</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">粗体</div>
                            <div class="detail-value">${element.bold ? '是' : '否'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">斜体</div>
                            <div class="detail-value">${element.italic ? '是' : '否'}</div>
                        </div>
                    `;
                } else if (element.elementType === '2') { // 条码
                    detailsHtml += `
                        <div class="detail-item">
                            <div class="detail-label">条码类型</div>
                            <div class="detail-value">${element.barcodeType || '未知'}</div>
                        </div>
                    `;
                } else if (element.elementType === '10') { // 表格
                    detailsHtml += `
                        <div class="detail-item">
                            <div class="detail-label">行数</div>
                            <div class="detail-value">${element.rows}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">列数</div>
                            <div class="detail-value">${element.cols}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">单元格数</div>
                            <div class="detail-value">${element.cells?.length || 0}</div>
                        </div>
                    `;
                }

                if (element.angle && element.angle !== 0) {
                    detailsHtml += `
                        <div class="detail-item">
                            <div class="detail-label">旋转角度</div>
                            <div class="detail-value">${element.angle}°</div>
                        </div>
                    `;
                }

                return `
                    <div class="result-card">
                        <div class="result-header">
                            <h4>元素 ${index + 1}</h4>
                            <span class="element-type">${typeName} (${element.elementType})</span>
                        </div>
                        <div class="element-content">"${element.content}"</div>
                        <div class="element-details">
                            ${detailsHtml}
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = elementsHtml;
        }

        // 获取元素类型名称
        function getElementTypeName(elementType) {
            const typeNames = {
                '1': '文本元素',
                '2': '一维码/条形码',
                '7': '二维码',
                '10': '表格'
            };
            return typeNames[elementType] || `未知类型`;
        }

        // 切换JSON查看器
        function toggleJsonViewer() {
            const viewer = document.getElementById('jsonViewer');
            viewer.style.display = viewer.style.display === 'none' ? 'block' : 'none';
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = '❌ ' + message;
            
            // 移除之前的错误信息
            const existingError = document.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }
            
            document.querySelector('.main-content').insertBefore(errorDiv, document.querySelector('.upload-section'));
            
            // 3秒后自动移除
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        // 显示成功信息
        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = '✅ ' + message;
            
            // 移除之前的成功信息
            const existingSuccess = document.querySelector('.success-message');
            if (existingSuccess) {
                existingSuccess.remove();
            }
            
            document.querySelector('.main-content').insertBefore(successDiv, document.querySelector('.upload-section'));
            
            // 3秒后自动移除
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // 页面加载完成后检查API状态
        window.addEventListener('load', async () => {
            const apiUrl = document.getElementById('apiUrl').value;
            try {
                const response = await fetch(apiUrl.replace('/recognize', '/docs'));
                if (response.ok) {
                    showSuccess('后端API连接正常');
                }
            } catch (error) {
                showError('无法连接到后端API，请确保服务器正在运行');
            }
        });
    </script>
</body>
</html>
