const axios = require('axios');
const fs = require('fs');

/**
 * TextIn API 服务类
 * 用于调用TextIn的通用文档解析API
 */
class TextInService {
  constructor() {
    this.baseURL = 'https://api.textin.com/ai/service/v1';
    this.endpoint = '/pdf_to_markdown';

    // 从环境变量获取认证信息
    this.appId = process.env.TEXTIN_APP_ID;
    this.secretCode = process.env.TEXTIN_SECRET_CODE;

    // 是否使用模拟模式（用于测试）
    this.mockMode = process.env.TEXTIN_MOCK_MODE === 'true' || !this.appId || !this.secretCode;
    if (this.mockMode) {
      console.log('TextIn API 使用模拟模式');
    }

    // 默认配置
    this.defaultConfig = {
      char_details: 1,        // 返回字符位置信息
      page_details: 1,        // 返回页面详细信息
      catalog_details: 0,     // 不需要目录信息
      dpi: 144,              // PDF坐标基准
      apply_document_tree: 0, // 不生成标题
      markdown_details: 1,    // 生成markdown详情
      table_flavor: 'html',   // 表格格式
      get_image: 'objects',   // 返回图像对象
      image_output_type: 'base64str', // 图片以base64返回
      parse_mode: 'scan',     // 仅按文字识别模式
      get_excel: 0,          // 不返回excel
      raw_ocr: 1,            // 返回全文识别结果
      paratext_mode: 'annotation', // 非正文内容展示模式
      formula_level: 2,       // 不识别公式
      apply_merge: 1          // 合并段落和表格
    };
  }

  /**
   * 设置API认证信息
   * @param {string} appId - TextIn应用ID
   * @param {string} secretCode - TextIn密钥
   */
  setCredentials(appId, secretCode) {
    this.appId = appId;
    this.secretCode = secretCode;
  }

  /**
   * 获取请求头
   * @returns {Object} 请求头对象
   */
  getHeaders() {
    if (!this.appId || !this.secretCode) {
      throw new Error('请先设置TextIn API认证信息 (TEXTIN_APP_ID, TEXTIN_SECRET_CODE)');
    }
    
    return {
      'x-ti-app-id': this.appId,
      'x-ti-secret-code': this.secretCode,
      'Content-Type': 'application/octet-stream'
    };
  }

  /**
   * 构建请求URL
   * @param {Object} params - URL参数
   * @returns {string} 完整的请求URL
   */
  buildUrl(params = {}) {
    const config = { ...this.defaultConfig, ...params };
    const url = new URL(this.baseURL + this.endpoint);
    
    Object.keys(config).forEach(key => {
      if (config[key] !== undefined && config[key] !== null) {
        url.searchParams.append(key, config[key]);
      }
    });
    
    return url.toString();
  }

  /**
   * 识别文件
   * @param {string} filePath - 文件路径
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} API响应结果
   */
  async recognizeFile(filePath, options = {}) {
    // 如果是模拟模式，返回模拟数据
    if (this.mockMode) {
      return this.getMockResponse(filePath);
    }

    try {
      const url = this.buildUrl(options);
      const headers = this.getHeaders();

      // 读取文件
      const fileBuffer = fs.readFileSync(filePath);

      console.log('发送TextIn API请求:', {
        url,
        fileSize: fileBuffer.length,
        filePath
      });

      const response = await axios.post(url, fileBuffer, {
        headers,
        timeout: 60000, // 60秒超时
        maxContentLength: 500 * 1024 * 1024, // 500MB
        maxBodyLength: 500 * 1024 * 1024
      });

      console.log('TextIn API响应状态:', response.data.code);

      if (response.data.code !== 200) {
        throw new Error(`TextIn API错误: ${response.data.message || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error('TextIn API调用失败:', error.message);

      if (error.response) {
        // 服务器响应错误
        const errorData = error.response.data;
        throw new Error(`TextIn API错误 (${error.response.status}): ${errorData.message || '服务器错误'}`);
      } else if (error.request) {
        // 网络错误
        throw new Error('网络错误: 无法连接到TextIn服务器');
      } else {
        // 其他错误
        throw new Error(`TextIn API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 通过URL识别文件
   * @param {string} fileUrl - 文件URL
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} API响应结果
   */
  async recognizeUrl(fileUrl, options = {}) {
    try {
      const url = this.buildUrl(options);
      const headers = {
        ...this.getHeaders(),
        'Content-Type': 'text/plain'
      };
      
      console.log('发送TextIn API请求 (URL):', {
        url,
        fileUrl
      });

      const response = await axios.post(url, fileUrl, {
        headers,
        timeout: 60000
      });

      console.log('TextIn API响应状态:', response.data.code);
      
      if (response.data.code !== 200) {
        throw new Error(`TextIn API错误: ${response.data.message || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error('TextIn API调用失败:', error.message);
      
      if (error.response) {
        const errorData = error.response.data;
        throw new Error(`TextIn API错误 (${error.response.status}): ${errorData.message || '服务器错误'}`);
      } else if (error.request) {
        throw new Error('网络错误: 无法连接到TextIn服务器');
      } else {
        throw new Error(`TextIn API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 验证API认证信息
   * @returns {boolean} 是否已设置认证信息
   */
  hasCredentials() {
    return !!(this.appId && this.secretCode);
  }

  /**
   * 获取模拟响应数据
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 模拟的API响应
   */
  async getMockResponse(filePath) {
    console.log('使用模拟TextIn API响应');

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      code: 200,
      message: 'success',
      result: {
        pages: [{
          content: [
            {
              id: 'text_1',
              type: 'line',
              text: 'Hello World!',
              pos: [50, 100, 250, 100, 250, 140, 50, 140],
              char_pos: [
                [50, 100, 70, 100, 70, 140, 50, 140],   // H
                [70, 100, 85, 100, 85, 140, 70, 140],   // e
                [85, 100, 95, 100, 95, 140, 85, 140],   // l
                [95, 100, 105, 100, 105, 140, 95, 140], // l
                [105, 100, 120, 100, 120, 140, 105, 140], // o
                [120, 100, 135, 100, 135, 140, 120, 140], // 空格
                [135, 100, 155, 100, 155, 140, 135, 140], // W
                [155, 100, 170, 100, 170, 140, 155, 140], // o
                [170, 100, 185, 100, 185, 140, 170, 140], // r
                [185, 100, 195, 100, 195, 140, 185, 140], // l
                [195, 100, 210, 100, 210, 140, 195, 140], // d
                [210, 100, 225, 100, 225, 140, 210, 140]  // !
              ]
            },
            {
              id: 'text_2',
              type: 'line',
              text: 'Test Image',
              pos: [50, 150, 200, 150, 200, 190, 50, 190],
              char_pos: [
                [50, 150, 70, 150, 70, 190, 50, 190],   // T
                [70, 150, 85, 150, 85, 190, 70, 190],   // e
                [85, 150, 100, 150, 100, 190, 85, 190], // s
                [100, 150, 115, 150, 115, 190, 100, 190], // t
                [115, 150, 130, 150, 130, 190, 115, 190], // 空格
                [130, 150, 145, 150, 145, 190, 130, 190], // I
                [145, 150, 160, 150, 160, 190, 145, 190], // m
                [160, 150, 175, 150, 175, 190, 160, 190], // a
                [175, 150, 190, 150, 190, 190, 175, 190], // g
                [190, 150, 205, 150, 205, 190, 190, 190]  // e
              ]
            },
            {
              id: 'text_3',
              type: 'line',
              text: '测试图片',
              pos: [50, 200, 180, 200, 180, 240, 50, 240],
              char_pos: [
                [50, 200, 82, 200, 82, 240, 50, 240],   // 测
                [82, 200, 114, 200, 114, 240, 82, 240], // 试
                [114, 200, 146, 200, 146, 240, 114, 240], // 图
                [146, 200, 178, 200, 178, 240, 146, 240]  // 片
              ]
            }
          ]
        }],
        structured: [
          {
            type: 'paragraph',
            content: ['text_1'],
            text: 'Hello World!',
            markdown: 'Hello World!',
            pos: [50, 100, 250, 100, 250, 140, 50, 140]
          },
          {
            type: 'paragraph',
            content: ['text_2'],
            text: 'Test Image',
            markdown: 'Test Image',
            pos: [50, 150, 200, 150, 200, 190, 50, 190]
          },
          {
            type: 'paragraph',
            content: ['text_3'],
            text: '测试图片',
            markdown: '测试图片',
            pos: [50, 200, 180, 200, 180, 240, 50, 240]
          }
        ]
      }
    };
  }
}

module.exports = TextInService;
