const axios = require('axios');
const fs = require('fs');

/**
 * TextIn API 服务类
 * 用于调用TextIn的通用文档解析API
 */
class TextInService {
  constructor() {
    this.baseURL = 'https://api.textin.com/ai/service/v1';
    this.endpoint = '/pdf_to_markdown';
    
    // 从环境变量获取认证信息
    this.appId = process.env.TEXTIN_APP_ID;
    this.secretCode = process.env.TEXTIN_SECRET_CODE;
    
    // 默认配置
    this.defaultConfig = {
      char_details: 1,        // 返回字符位置信息
      page_details: 1,        // 返回页面详细信息
      catalog_details: 0,     // 不需要目录信息
      dpi: 144,              // PDF坐标基准
      apply_document_tree: 0, // 不生成标题
      markdown_details: 1,    // 生成markdown详情
      table_flavor: 'html',   // 表格格式
      get_image: 'objects',   // 返回图像对象
      image_output_type: 'base64str', // 图片以base64返回
      parse_mode: 'scan',     // 仅按文字识别模式
      get_excel: 0,          // 不返回excel
      raw_ocr: 1,            // 返回全文识别结果
      paratext_mode: 'annotation', // 非正文内容展示模式
      formula_level: 2,       // 不识别公式
      apply_merge: 1          // 合并段落和表格
    };
  }

  /**
   * 设置API认证信息
   * @param {string} appId - TextIn应用ID
   * @param {string} secretCode - TextIn密钥
   */
  setCredentials(appId, secretCode) {
    this.appId = appId;
    this.secretCode = secretCode;
  }

  /**
   * 获取请求头
   * @returns {Object} 请求头对象
   */
  getHeaders() {
    if (!this.appId || !this.secretCode) {
      throw new Error('请先设置TextIn API认证信息 (TEXTIN_APP_ID, TEXTIN_SECRET_CODE)');
    }
    
    return {
      'x-ti-app-id': this.appId,
      'x-ti-secret-code': this.secretCode,
      'Content-Type': 'application/octet-stream'
    };
  }

  /**
   * 构建请求URL
   * @param {Object} params - URL参数
   * @returns {string} 完整的请求URL
   */
  buildUrl(params = {}) {
    const config = { ...this.defaultConfig, ...params };
    const url = new URL(this.baseURL + this.endpoint);
    
    Object.keys(config).forEach(key => {
      if (config[key] !== undefined && config[key] !== null) {
        url.searchParams.append(key, config[key]);
      }
    });
    
    return url.toString();
  }

  /**
   * 识别文件
   * @param {string} filePath - 文件路径
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} API响应结果
   */
  async recognizeFile(filePath, options = {}) {
    try {
      const url = this.buildUrl(options);
      const headers = this.getHeaders();
      
      // 读取文件
      const fileBuffer = fs.readFileSync(filePath);
      
      console.log('发送TextIn API请求:', {
        url,
        fileSize: fileBuffer.length,
        filePath
      });

      const response = await axios.post(url, fileBuffer, {
        headers,
        timeout: 60000, // 60秒超时
        maxContentLength: 500 * 1024 * 1024, // 500MB
        maxBodyLength: 500 * 1024 * 1024
      });

      console.log('TextIn API响应状态:', response.data.code);
      
      if (response.data.code !== 200) {
        throw new Error(`TextIn API错误: ${response.data.message || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error('TextIn API调用失败:', error.message);
      
      if (error.response) {
        // 服务器响应错误
        const errorData = error.response.data;
        throw new Error(`TextIn API错误 (${error.response.status}): ${errorData.message || '服务器错误'}`);
      } else if (error.request) {
        // 网络错误
        throw new Error('网络错误: 无法连接到TextIn服务器');
      } else {
        // 其他错误
        throw new Error(`TextIn API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 通过URL识别文件
   * @param {string} fileUrl - 文件URL
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} API响应结果
   */
  async recognizeUrl(fileUrl, options = {}) {
    try {
      const url = this.buildUrl(options);
      const headers = {
        ...this.getHeaders(),
        'Content-Type': 'text/plain'
      };
      
      console.log('发送TextIn API请求 (URL):', {
        url,
        fileUrl
      });

      const response = await axios.post(url, fileUrl, {
        headers,
        timeout: 60000
      });

      console.log('TextIn API响应状态:', response.data.code);
      
      if (response.data.code !== 200) {
        throw new Error(`TextIn API错误: ${response.data.message || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error('TextIn API调用失败:', error.message);
      
      if (error.response) {
        const errorData = error.response.data;
        throw new Error(`TextIn API错误 (${error.response.status}): ${errorData.message || '服务器错误'}`);
      } else if (error.request) {
        throw new Error('网络错误: 无法连接到TextIn服务器');
      } else {
        throw new Error(`TextIn API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 验证API认证信息
   * @returns {boolean} 是否已设置认证信息
   */
  hasCredentials() {
    return !!(this.appId && this.secretCode);
  }
}

module.exports = TextInService;
