const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

// 加载环境变量
require('dotenv').config();

/**
 * 调试条码识别问题
 */
async function debugBarcodeRecognition() {
  console.log('🔍 调试条码识别问题...\n');

  // 检查环境变量
  console.log('📋 环境变量检查:');
  console.log(`TEXTIN_APP_ID: ${process.env.TEXTIN_APP_ID ? '已设置' : '未设置'}`);
  console.log(`TEXTIN_SECRET_CODE: ${process.env.TEXTIN_SECRET_CODE ? '已设置' : '未设置'}`);
  console.log('');

  // 测试肥牛卷图片（应该包含条码）
  const imagePath = path.join(__dirname, 'data', '肥牛卷（40_30）.png');
  
  if (!fs.existsSync(imagePath)) {
    console.error('❌ 测试图片不存在:', imagePath);
    return;
  }

  console.log('📷 测试图片:', imagePath);
  
  try {
    // 1. 直接调用TextIn API
    console.log('\n🔧 步骤1: 直接调用TextIn API...');
    const textinResult = await callTextInAPI(imagePath);
    
    // 保存原始TextIn响应
    const rawOutputPath = path.join(__dirname, 'debug-textin-raw.json');
    fs.writeFileSync(rawOutputPath, JSON.stringify(textinResult, null, 2));
    console.log(`💾 原始TextIn响应已保存到: ${rawOutputPath}`);
    
    // 2. 分析TextIn响应中的图像元素
    console.log('\n🔍 步骤2: 分析TextIn响应中的图像元素...');
    analyzeTextInImages(textinResult);
    
    // 3. 调用我们的API
    console.log('\n🚀 步骤3: 调用我们的API...');
    const apiResult = await callOurAPI(imagePath);
    
    // 保存API响应
    const apiOutputPath = path.join(__dirname, 'debug-api-result.json');
    fs.writeFileSync(apiOutputPath, JSON.stringify(apiResult, null, 2));
    console.log(`💾 API响应已保存到: ${apiOutputPath}`);
    
    // 4. 分析条码元素
    console.log('\n📊 步骤4: 分析条码元素...');
    analyzeBarcodeElements(apiResult);
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error.message);
  }
}

/**
 * 直接调用TextIn API
 */
async function callTextInAPI(imagePath) {
  const TextInService = require('./services/textinService');
  const textinService = new TextInService();
  
  console.log('发送TextIn API请求...');
  const result = await textinService.recognizeFile(imagePath);
  console.log('✅ TextIn API调用成功');
  
  return result;
}

/**
 * 分析TextIn响应中的图像元素
 */
function analyzeTextInImages(textinResult) {
  const { result } = textinResult;
  if (!result || !result.pages) {
    console.log('❌ TextIn响应格式不正确');
    return;
  }

  const firstPage = result.pages[0];
  if (!firstPage || !firstPage.content) {
    console.log('❌ 没有找到页面内容');
    return;
  }

  const imageElements = firstPage.content.filter(item => item.type === 'image');
  console.log(`📷 找到 ${imageElements.length} 个图像元素:`);
  
  imageElements.forEach((img, index) => {
    console.log(`\n图像 ${index + 1}:`);
    console.log(`- 类型: ${img.type}`);
    console.log(`- 子类型: ${img.sub_type || '未知'}`);
    console.log(`- 文本: "${img.text || '无'}"`);
    console.log(`- 位置: [${img.pos?.join(', ') || '无'}]`);
    console.log(`- 是否有base64数据: ${!!(img.data?.base64 || img.base64str)}`);
    
    if (img.data?.base64 || img.base64str) {
      const base64Data = img.data?.base64 || img.base64str;
      console.log(`- Base64数据长度: ${base64Data.length} 字符`);
      console.log(`- Base64前缀: ${base64Data.substring(0, 50)}...`);
    }
  });
}

/**
 * 调用我们的API
 */
async function callOurAPI(imagePath) {
  const formData = new FormData();
  formData.append('image', fs.createReadStream(imagePath));
  
  console.log('发送API请求...');
  const response = await axios.post('http://localhost:3001/api/recognize', formData, {
    headers: {
      ...formData.getHeaders(),
    },
    maxContentLength: Infinity,
    maxBodyLength: Infinity
  });
  
  console.log('✅ API调用成功');
  return response.data;
}

/**
 * 分析条码元素
 */
function analyzeBarcodeElements(apiResult) {
  if (!apiResult.success) {
    console.log('❌ API调用失败:', apiResult.error);
    return;
  }

  const { elements } = apiResult.data;
  const barcodeElements = elements.filter(el => el.elementType === '2' || el.elementType === '7');
  
  console.log(`🔍 找到 ${barcodeElements.length} 个条码元素:`);
  
  if (barcodeElements.length === 0) {
    console.log('⚠️  没有找到条码元素，可能的原因:');
    console.log('1. 图片中确实没有条码');
    console.log('2. TextIn API没有识别出条码');
    console.log('3. 我们的转换逻辑有问题');
    
    // 显示所有元素类型
    console.log('\n📋 所有识别到的元素类型:');
    elements.forEach((el, index) => {
      console.log(`${index + 1}. elementType: ${el.elementType}, content: "${el.content}"`);
    });
  } else {
    barcodeElements.forEach((barcode, index) => {
      console.log(`\n条码 ${index + 1}:`);
      console.log(`- 元素类型: ${barcode.elementType}`);
      console.log(`- 内容: "${barcode.content}"`);
      console.log(`- 位置: (${barcode.x}, ${barcode.y})`);
      console.log(`- 尺寸: ${barcode.width} x ${barcode.height}`);
      if (barcode.barcodeType) {
        console.log(`- 条码类型: ${barcode.barcodeType}`);
      }
    });
  }
}

// 执行调试
debugBarcodeRecognition().then(() => {
  console.log('\n✅ 调试完成!');
}).catch(error => {
  console.error('\n❌ 调试失败:', error.message);
});
