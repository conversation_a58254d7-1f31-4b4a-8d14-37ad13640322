# 图片识别后端API

基于Node.js和Express的图片识别后端服务，集成TextIn API，为APP端提供图片识别和数据转换功能。

## 功能特性

- 图片上传和识别
- TextIn API集成
- 文本、条码、二维码、表格识别
- 字符宽度计算（供APP端计算字体大小）
- 坐标和尺寸数据输出（像素单位）
- 条码内容解析

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置TextIn API认证信息：
```
TEXTIN_APP_ID=your_textin_app_id_here
TEXTIN_SECRET_CODE=your_textin_secret_code_here
```

### 3. 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

服务将在 `http://localhost:3001` 启动。

## API接口

### POST /api/recognize

上传图片进行识别

**请求参数：**
- `image`: 图片文件 (multipart/form-data)

**响应格式：**
```json
{
  "success": true,
  "data": {
    "imageInfo": {
      "width": 1080,
      "height": 1920,
      "format": "jpeg"
    },
    "elements": [
      {
        "elementType": "1",
        "x": 100,
        "y": 200,
        "width": 300,
        "height": 50,
        "content": "示例文本",
        "charWidth": 25.5,
        "bold": false,
        "italic": false,
        "angle": 0
      }
    ]
  },
  "message": "识别成功"
}
```

### GET /health

健康检查接口

### GET /api/docs

API文档接口

## 元素类型

- `"1"`: 文本元素
- `"2"`: 一维码/条形码
- `"7"`: 二维码
- `"10"`: 表格

## 项目结构

```
backend/
├── services/           # 服务层
│   ├── textinService.js    # TextIn API调用
│   ├── dataConverter.js    # 数据转换
│   ├── imageProcessor.js   # 图片处理
│   └── barcodeDecoder.js   # 条码解析
├── routes/             # 路由层
│   └── recognition.js      # 识别接口路由
├── uploads/            # 文件上传目录
├── server.js           # 服务器入口
├── package.json        # 项目配置
└── README.md          # 项目文档
```

## 开发说明

### 字符宽度计算

后端提供 `charWidth` 字段（单个字符的平均宽度，像素单位），APP端可根据此值计算合适的字体大小。

### 坐标系统

所有坐标和尺寸都使用像素(px)单位，相对于原图片尺寸，左上角为(0,0)原点。

### 条码识别

使用@zxing/library进行条码和二维码内容解析，支持多种条码格式。

## 错误处理

API返回统一的错误格式：
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  }
}
```

常见错误码：
- `INVALID_IMAGE`: 无效的图片文件
- `RECOGNITION_FAILED`: TextIn API识别失败
- `PROCESSING_ERROR`: 数据处理错误
- `INTERNAL_ERROR`: 服务器内部错误

## 依赖说明

- `express`: Web框架
- `multer`: 文件上传中间件
- `axios`: HTTP客户端（调用TextIn API）
- `sharp`: 图片处理
- `@zxing/library`: 条码识别
- `jimp`: 图片处理（条码解析）
