/**
 * 测试上传配置和环境变量使用
 */

// 加载环境变量
require('dotenv').config();
const path = require('path');
const fs = require('fs');

console.log('🔧 测试上传配置和环境变量使用...\n');

/**
 * 解析文件大小
 */
function parseFileSize(sizeStr) {
  if (!sizeStr) return 50 * 1024 * 1024; // 默认50MB
  
  const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(MB|KB|GB)?$/i);
  if (!match) return 50 * 1024 * 1024;
  
  const size = parseFloat(match[1]);
  const unit = (match[2] || 'MB').toUpperCase();
  
  switch (unit) {
    case 'KB': return size * 1024;
    case 'MB': return size * 1024 * 1024;
    case 'GB': return size * 1024 * 1024 * 1024;
    default: return size * 1024 * 1024; // 默认MB
  }
}

/**
 * 获取上传目录路径
 */
function getUploadDir() {
  const uploadDirFromEnv = process.env.UPLOAD_DIR || './uploads';
  return path.isAbsolute(uploadDirFromEnv) 
    ? uploadDirFromEnv 
    : path.join(__dirname, uploadDirFromEnv);
}

/**
 * 格式化文件大小显示
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 测试环境变量读取
console.log('📋 环境变量配置:');
console.log(`UPLOAD_DIR: ${process.env.UPLOAD_DIR || '未设置 (使用默认值)'}`);
console.log(`MAX_FILE_SIZE: ${process.env.MAX_FILE_SIZE || '未设置 (使用默认值)'}`);
console.log('');

// 测试上传目录解析
console.log('📁 上传目录解析:');
const uploadDir = getUploadDir();
console.log(`解析后的上传目录: ${uploadDir}`);
console.log(`是否为绝对路径: ${path.isAbsolute(uploadDir)}`);
console.log(`目录是否存在: ${fs.existsSync(uploadDir)}`);

// 创建目录（如果不存在）
if (!fs.existsSync(uploadDir)) {
  try {
    fs.mkdirSync(uploadDir, { recursive: true });
    console.log(`✅ 成功创建上传目录: ${uploadDir}`);
  } catch (error) {
    console.error(`❌ 创建上传目录失败: ${error.message}`);
  }
} else {
  console.log(`✅ 上传目录已存在: ${uploadDir}`);
}

// 测试文件大小解析
console.log('\n📊 文件大小限制解析:');
const maxFileSize = parseFileSize(process.env.MAX_FILE_SIZE);
console.log(`解析后的文件大小限制: ${formatFileSize(maxFileSize)}`);

// 测试不同的文件大小格式
console.log('\n🧪 测试不同文件大小格式:');
const testSizes = ['10MB', '5GB', '500KB', '1.5MB', '100', '', 'invalid'];
testSizes.forEach(size => {
  const parsed = parseFileSize(size);
  console.log(`"${size}" -> ${formatFileSize(parsed)}`);
});

// 测试静态文件服务路径
console.log('\n🌐 静态文件服务配置:');
const uploadDirFromEnv = process.env.UPLOAD_DIR || './uploads';
const staticUploadDir = path.isAbsolute(uploadDirFromEnv) 
  ? uploadDirFromEnv 
  : path.join(__dirname, uploadDirFromEnv);
console.log(`静态文件服务目录: ${staticUploadDir}`);
console.log(`与上传目录一致: ${staticUploadDir === uploadDir}`);

// 检查目录权限
console.log('\n🔐 目录权限检查:');
try {
  // 测试写入权限
  const testFile = path.join(uploadDir, 'test-write-permission.txt');
  fs.writeFileSync(testFile, 'test');
  fs.unlinkSync(testFile);
  console.log('✅ 目录具有读写权限');
} catch (error) {
  console.error(`❌ 目录权限检查失败: ${error.message}`);
}

// 显示配置总结
console.log('\n📋 配置总结:');
console.log('='.repeat(50));
console.log(`上传目录: ${uploadDir}`);
console.log(`文件大小限制: ${formatFileSize(maxFileSize)}`);
console.log(`静态文件服务: /uploads -> ${staticUploadDir}`);
console.log('='.repeat(50));

// 提供配置建议
console.log('\n💡 配置建议:');
if (!process.env.UPLOAD_DIR) {
  console.log('- 建议在.env文件中设置UPLOAD_DIR环境变量');
}
if (!process.env.MAX_FILE_SIZE) {
  console.log('- 建议在.env文件中设置MAX_FILE_SIZE环境变量');
}

console.log('\n✅ 测试完成!');
