const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 导入服务模块
const TextInService = require('../services/textinService');
const DataConverter = require('../services/dataConverter');
const ImageProcessor = require('../services/imageProcessor');

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedTypes = /jpeg|jpg|png|gif|bmp|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只支持图片文件格式'));
    }
  }
});

// 初始化服务
const textInService = new TextInService();
const dataConverter = new DataConverter();
const imageProcessor = new ImageProcessor();

/**
 * POST /api/recognize
 * 图片识别接口
 */
router.post('/recognize', upload.single('image'), async (req, res) => {
  try {
    // 检查文件是否上传成功
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_IMAGE',
          message: '请上传图片文件'
        }
      });
    }

    console.log('收到图片识别请求:', {
      filename: req.file.filename,
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    });

    // 获取图片信息
    const imageInfo = await imageProcessor.getImageInfo(req.file.path);
    console.log('图片信息:', imageInfo);

    // 调用TextIn API进行识别
    console.log('开始调用TextIn API...');
    const textinResult = await textInService.recognizeFile(req.file.path);
    console.log('TextIn API识别完成');

    // 转换数据格式
    console.log('开始转换数据格式...');
    const elements = await dataConverter.convertToAppFormat(textinResult, imageInfo);
    console.log(`数据转换完成，共识别到 ${elements.length} 个元素`);

    // 清理临时文件
    try {
      fs.unlinkSync(req.file.path);
    } catch (cleanupError) {
      console.warn('清理临时文件失败:', cleanupError.message);
    }

    // 返回结果
    res.json({
      success: true,
      data: {
        imageInfo: {
          width: imageInfo.width,
          height: imageInfo.height,
          format: imageInfo.format
        },
        elements: elements
      },
      message: '识别成功'
    });

  } catch (error) {
    console.error('图片识别失败:', error);

    // 清理临时文件
    if (req.file && req.file.path) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError.message);
      }
    }

    // 根据错误类型返回不同的错误码
    let errorCode = 'PROCESSING_ERROR';
    let errorMessage = error.message;

    if (error.message.includes('TextIn')) {
      errorCode = 'RECOGNITION_FAILED';
    } else if (error.message.includes('图片')) {
      errorCode = 'INVALID_IMAGE';
    }

    res.status(500).json({
      success: false,
      error: {
        code: errorCode,
        message: errorMessage
      }
    });
  }
});

/**
 * GET /api/docs
 * API文档接口
 */
router.get('/docs', (req, res) => {
  res.json({
    title: '图片识别API文档',
    version: '1.0.0',
    endpoints: {
      'POST /api/recognize': {
        description: '上传图片进行识别',
        parameters: {
          image: '图片文件 (multipart/form-data)',
          imageWidth: '图片宽度(px) - 可选',
          imageHeight: '图片高度(px) - 可选'
        },
        response: {
          success: 'boolean',
          data: {
            imageInfo: 'object',
            elements: 'array'
          },
          message: 'string'
        }
      }
    }
  });
});

module.exports = router;
