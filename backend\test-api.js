const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

/**
 * 测试图片识别API
 */
async function testRecognizeAPI() {
  try {
    const apiUrl = 'http://localhost:3001/api/recognize';
    const testImagePath = path.join(__dirname, 'test-image.jpg');
    
    // 检查测试图片是否存在
    if (!fs.existsSync(testImagePath)) {
      console.error('测试图片不存在:', testImagePath);
      console.log('请将测试图片放在backend目录下，并命名为test-image.jpg');
      return;
    }
    
    console.log('开始测试图片识别API...');
    console.log('API地址:', apiUrl);
    console.log('测试图片:', testImagePath);
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));
    
    // 发送请求
    console.log('发送请求中...');
    const response = await axios.post(apiUrl, formData, {
      headers: {
        ...formData.getHeaders(),
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });
    
    console.log('请求成功!');
    console.log('状态码:', response.status);
    
    // 检查响应格式
    if (response.data.success) {
      console.log('识别成功!');
      
      const { imageInfo, elements } = response.data.data;
      
      console.log('\n图片信息:');
      console.log(`- 宽度: ${imageInfo.width}px`);
      console.log(`- 高度: ${imageInfo.height}px`);
      console.log(`- 格式: ${imageInfo.format}`);
      
      console.log(`\n识别到 ${elements.length} 个元素:`);
      
      // 按类型统计元素
      const elementCounts = {
        '1': 0, // 文本
        '2': 0, // 条码
        '7': 0, // 二维码
        '10': 0 // 表格
      };
      
      elements.forEach(element => {
        elementCounts[element.elementType] = (elementCounts[element.elementType] || 0) + 1;
      });
      
      console.log('元素类型统计:');
      console.log(`- 文本元素: ${elementCounts['1'] || 0}个`);
      console.log(`- 条码元素: ${elementCounts['2'] || 0}个`);
      console.log(`- 二维码元素: ${elementCounts['7'] || 0}个`);
      console.log(`- 表格元素: ${elementCounts['10'] || 0}个`);
      
      // 输出部分元素示例
      if (elements.length > 0) {
        console.log('\n元素示例:');
        
        // 文本元素示例
        const textElement = elements.find(e => e.elementType === '1');
        if (textElement) {
          console.log('\n文本元素示例:');
          console.log(`- 内容: "${textElement.content}"`);
          console.log(`- 位置: (${textElement.x}, ${textElement.y})`);
          console.log(`- 尺寸: ${textElement.width} x ${textElement.height}`);
          console.log(`- 字符宽度: ${textElement.charWidth}`);
          console.log(`- 粗体: ${textElement.bold}`);
          console.log(`- 斜体: ${textElement.italic}`);
        }
        
        // 条码元素示例
        const barcodeElement = elements.find(e => e.elementType === '2');
        if (barcodeElement) {
          console.log('\n条码元素示例:');
          console.log(`- 内容: "${barcodeElement.content}"`);
          console.log(`- 位置: (${barcodeElement.x}, ${barcodeElement.y})`);
          console.log(`- 尺寸: ${barcodeElement.width} x ${barcodeElement.height}`);
          console.log(`- 类型: ${barcodeElement.barcodeType}`);
        }
        
        // 表格元素示例
        const tableElement = elements.find(e => e.elementType === '10');
        if (tableElement) {
          console.log('\n表格元素示例:');
          console.log(`- 位置: (${tableElement.x}, ${tableElement.y})`);
          console.log(`- 尺寸: ${tableElement.width} x ${tableElement.height}`);
          console.log(`- 行数: ${tableElement.rows}`);
          console.log(`- 列数: ${tableElement.cols}`);
          console.log(`- 单元格数: ${tableElement.cells.length}`);
          
          if (tableElement.cells.length > 0) {
            const cell = tableElement.cells[0];
            console.log(`- 单元格示例 (${cell.row}, ${cell.col}): "${cell.content}"`);
          }
        }
      }
      
      // 保存响应数据到文件
      const outputPath = path.join(__dirname, 'test-result.json');
      fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2));
      console.log(`\n完整响应数据已保存到: ${outputPath}`);
      
    } else {
      console.error('识别失败:', response.data.error);
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    
    if (error.response) {
      console.error('响应状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 执行测试
testRecognizeAPI();
