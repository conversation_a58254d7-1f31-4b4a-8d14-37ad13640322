const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');
const Jimp = require('jimp');

/**
 * 测试真实图片的基本信息和API响应
 */
async function testRealImages() {
  const dataDir = path.join(__dirname, 'data');
  const apiUrl = 'http://localhost:3001/api/recognize';
  
  // 获取data目录中的所有图片文件
  const imageFiles = fs.readdirSync(dataDir).filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif', '.bmp'].includes(ext);
  });

  if (imageFiles.length === 0) {
    console.log('data目录中没有找到图片文件');
    return;
  }

  console.log(`🖼️  找到 ${imageFiles.length} 张测试图片:`);
  imageFiles.forEach((file, index) => {
    console.log(`${index + 1}. ${file}`);
  });
  console.log('');

  // 首先分析图片的基本信息
  console.log('📊 图片基本信息分析:');
  console.log('='.repeat(80));
  
  for (let i = 0; i < imageFiles.length; i++) {
    const imageFile = imageFiles[i];
    const imagePath = path.join(dataDir, imageFile);
    
    try {
      // 使用Jimp分析图片
      const image = await Jimp.read(imagePath);
      const stats = fs.statSync(imagePath);
      
      console.log(`\n${i + 1}. ${imageFile}:`);
      console.log(`   📏 尺寸: ${image.getWidth()} x ${image.getHeight()}px`);
      console.log(`   💾 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log(`   🎨 格式: ${path.extname(imageFile).substring(1).toUpperCase()}`);
      console.log(`   🔍 像素密度: ${(image.getWidth() * image.getHeight() / 1000000).toFixed(2)}M 像素`);
      
      // 分析图片内容（简单的颜色分析）
      const colorInfo = analyzeImageColors(image);
      console.log(`   🌈 主要颜色: ${colorInfo.dominantColor}`);
      console.log(`   ⚫ 平均亮度: ${colorInfo.averageBrightness}%`);
      
    } catch (error) {
      console.error(`   ❌ 分析失败: ${error.message}`);
    }
  }

  console.log('\n' + '='.repeat(80));
  console.log('🚀 开始API识别测试...\n');

  // 测试API识别
  for (let i = 0; i < imageFiles.length; i++) {
    const imageFile = imageFiles[i];
    const imagePath = path.join(dataDir, imageFile);
    
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🔍 测试图片 ${i + 1}/${imageFiles.length}: ${imageFile}`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      await testImageRecognition(imagePath, imageFile, apiUrl);
    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }
    
    // 在测试之间添加短暂延迟
    if (i < imageFiles.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // 提供使用真实API的建议
  console.log('\n' + '='.repeat(80));
  console.log('💡 使用真实TextIn API的方法:');
  console.log('1. 在 .env 文件中设置真实的API密钥:');
  console.log('   TEXTIN_APP_ID=your_real_app_id');
  console.log('   TEXTIN_SECRET_CODE=your_real_secret_code');
  console.log('2. 重启服务器: npm start');
  console.log('3. 重新运行测试');
  console.log('='.repeat(80));
}

/**
 * 分析图片颜色信息
 */
function analyzeImageColors(image) {
  const width = image.getWidth();
  const height = image.getHeight();
  const sampleSize = Math.min(100, width * height); // 采样100个像素
  
  let totalR = 0, totalG = 0, totalB = 0;
  let totalBrightness = 0;
  
  for (let i = 0; i < sampleSize; i++) {
    const x = Math.floor(Math.random() * width);
    const y = Math.floor(Math.random() * height);
    const color = image.getPixelColor(x, y);
    
    const rgba = Jimp.intToRGBA(color);
    totalR += rgba.r;
    totalG += rgba.g;
    totalB += rgba.b;
    
    // 计算亮度 (0.299*R + 0.587*G + 0.114*B)
    const brightness = 0.299 * rgba.r + 0.587 * rgba.g + 0.114 * rgba.b;
    totalBrightness += brightness;
  }
  
  const avgR = Math.round(totalR / sampleSize);
  const avgG = Math.round(totalG / sampleSize);
  const avgB = Math.round(totalB / sampleSize);
  const avgBrightness = Math.round((totalBrightness / sampleSize / 255) * 100);
  
  // 确定主要颜色
  let dominantColor = 'Gray';
  if (avgR > avgG && avgR > avgB) {
    dominantColor = avgR > 150 ? 'Red' : 'Dark Red';
  } else if (avgG > avgR && avgG > avgB) {
    dominantColor = avgG > 150 ? 'Green' : 'Dark Green';
  } else if (avgB > avgR && avgB > avgG) {
    dominantColor = avgB > 150 ? 'Blue' : 'Dark Blue';
  } else if (avgBrightness > 200) {
    dominantColor = 'White';
  } else if (avgBrightness < 50) {
    dominantColor = 'Black';
  }
  
  return {
    dominantColor,
    averageBrightness: avgBrightness,
    avgRGB: `RGB(${avgR}, ${avgG}, ${avgB})`
  };
}

/**
 * 测试图片识别API
 */
async function testImageRecognition(imagePath, fileName, apiUrl) {
  try {
    // 创建FormData对象
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));
    
    console.log('📤 发送识别请求...');
    const startTime = Date.now();
    
    // 发送请求
    const response = await axios.post(apiUrl, formData, {
      headers: {
        ...formData.getHeaders(),
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      timeout: 30000 // 30秒超时
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ 请求完成! 耗时: ${duration}ms`);
    
    if (response.data.success) {
      const { imageInfo, elements } = response.data.data;
      
      console.log('\n📷 API返回的图片信息:');
      console.log(`   尺寸: ${imageInfo.width} x ${imageInfo.height}px`);
      console.log(`   格式: ${imageInfo.format}`);
      
      console.log(`\n🔍 识别结果: ${elements.length} 个元素`);
      
      // 统计元素类型
      const typeCounts = {};
      elements.forEach(el => {
        typeCounts[el.elementType] = (typeCounts[el.elementType] || 0) + 1;
      });
      
      Object.keys(typeCounts).forEach(type => {
        const typeName = getElementTypeName(type);
        console.log(`   ${typeName}: ${typeCounts[type]}个`);
      });
      
      // 显示前3个元素的详细信息
      if (elements.length > 0) {
        console.log('\n📝 元素详情 (前3个):');
        elements.slice(0, 3).forEach((element, index) => {
          console.log(`   ${index + 1}. ${getElementTypeName(element.elementType)}: "${element.content}"`);
          console.log(`      位置: (${element.x}, ${element.y}) 尺寸: ${element.width}x${element.height}px`);
          if (element.charWidth) {
            console.log(`      字符宽度: ${element.charWidth}px`);
          }
        });
        
        if (elements.length > 3) {
          console.log(`   ... 还有 ${elements.length - 3} 个元素`);
        }
      }
      
    } else {
      console.error('❌ 识别失败:', response.data.error);
    }
    
  } catch (error) {
    console.error('❌ API调用失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      if (error.response.data) {
        console.error('   错误信息:', error.response.data.error?.message || error.response.data);
      }
    }
  }
}

/**
 * 获取元素类型名称
 */
function getElementTypeName(elementType) {
  const typeNames = {
    '1': '文本',
    '2': '一维码',
    '7': '二维码',
    '10': '表格'
  };
  return typeNames[elementType] || `未知(${elementType})`;
}

// 执行测试
console.log('🎯 开始测试真实图片...\n');
testRealImages().then(() => {
  console.log('\n🎉 测试完成!');
}).catch(error => {
  console.error('\n💥 测试过程中发生错误:', error.message);
});
