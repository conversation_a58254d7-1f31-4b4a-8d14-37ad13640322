const sharp = require('sharp');
const path = require('path');

/**
 * 图片处理服务
 * 用于获取图片信息、处理图片等
 */
class ImageProcessor {
  constructor() {
    this.supportedFormats = ['jpeg', 'jpg', 'png', 'gif', 'bmp', 'webp'];
  }

  /**
   * 获取图片基本信息
   * @param {string} imagePath - 图片文件路径
   * @returns {Promise<Object>} 图片信息
   */
  async getImageInfo(imagePath) {
    try {
      const metadata = await sharp(imagePath).metadata();
      
      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: metadata.size,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        channels: metadata.channels
      };
    } catch (error) {
      throw new Error(`获取图片信息失败: ${error.message}`);
    }
  }

  /**
   * 验证图片格式
   * @param {string} imagePath - 图片文件路径
   * @returns {boolean} 是否为支持的格式
   */
  async isValidImage(imagePath) {
    try {
      const metadata = await sharp(imagePath).metadata();
      return this.supportedFormats.includes(metadata.format.toLowerCase());
    } catch (error) {
      return false;
    }
  }

  /**
   * 压缩图片（如果需要）
   * @param {string} inputPath - 输入图片路径
   * @param {string} outputPath - 输出图片路径
   * @param {Object} options - 压缩选项
   * @returns {Promise<Object>} 压缩后的图片信息
   */
  async compressImage(inputPath, outputPath, options = {}) {
    try {
      const {
        maxWidth = 2048,
        maxHeight = 2048,
        quality = 85,
        format = 'jpeg'
      } = options;

      const result = await sharp(inputPath)
        .resize(maxWidth, maxHeight, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({ quality })
        .toFile(outputPath);

      return {
        width: result.width,
        height: result.height,
        size: result.size,
        format: format
      };
    } catch (error) {
      throw new Error(`图片压缩失败: ${error.message}`);
    }
  }

  /**
   * 转换图片格式
   * @param {string} inputPath - 输入图片路径
   * @param {string} outputPath - 输出图片路径
   * @param {string} format - 目标格式
   * @returns {Promise<Object>} 转换后的图片信息
   */
  async convertFormat(inputPath, outputPath, format = 'jpeg') {
    try {
      let sharpInstance = sharp(inputPath);

      switch (format.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
          sharpInstance = sharpInstance.jpeg({ quality: 90 });
          break;
        case 'png':
          sharpInstance = sharpInstance.png({ compressionLevel: 6 });
          break;
        case 'webp':
          sharpInstance = sharpInstance.webp({ quality: 90 });
          break;
        default:
          throw new Error(`不支持的图片格式: ${format}`);
      }

      const result = await sharpInstance.toFile(outputPath);
      
      return {
        width: result.width,
        height: result.height,
        size: result.size,
        format: format
      };
    } catch (error) {
      throw new Error(`图片格式转换失败: ${error.message}`);
    }
  }

  /**
   * 获取图片的像素密度信息
   * @param {string} imagePath - 图片文件路径
   * @returns {Promise<Object>} 像素密度信息
   */
  async getPixelDensity(imagePath) {
    try {
      const metadata = await sharp(imagePath).metadata();
      
      // 默认DPI值
      const defaultDPI = 72;
      const dpi = metadata.density || defaultDPI;
      
      return {
        dpi: dpi,
        pixelsPerMm: dpi / 25.4, // 1英寸 = 25.4毫米
        mmPerPixel: 25.4 / dpi
      };
    } catch (error) {
      throw new Error(`获取像素密度失败: ${error.message}`);
    }
  }

  /**
   * 计算图片的物理尺寸（毫米）
   * @param {string} imagePath - 图片文件路径
   * @returns {Promise<Object>} 物理尺寸信息
   */
  async getPhysicalSize(imagePath) {
    try {
      const imageInfo = await this.getImageInfo(imagePath);
      const densityInfo = await this.getPixelDensity(imagePath);
      
      const widthMm = imageInfo.width * densityInfo.mmPerPixel;
      const heightMm = imageInfo.height * densityInfo.mmPerPixel;
      
      return {
        widthMm: Math.round(widthMm * 100) / 100,
        heightMm: Math.round(heightMm * 100) / 100,
        widthPx: imageInfo.width,
        heightPx: imageInfo.height,
        dpi: densityInfo.dpi
      };
    } catch (error) {
      throw new Error(`计算物理尺寸失败: ${error.message}`);
    }
  }
}

module.exports = ImageProcessor;
