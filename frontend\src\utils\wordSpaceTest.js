/**
 * 字间距计算功能测试
 */

import { WordSpaceCalculator } from './wordSpaceCalculator';

// 创建测试实例
const calculator = new WordSpaceCalculator();

// 测试数据（来自test-data.json）
const testData = [
  {
    text: "解冻标签",
    char_pos: [
      [203, 38, 244, 38, 244, 76, 203, 76], // 解
      [287, 36, 328, 37, 328, 75, 287, 75], // 冻
      [370, 37, 413, 37, 413, 77, 370, 77], // 标
      [454, 37, 497, 37, 497, 77, 454, 76]  // 签
    ],
    pos: [200, 33, 500, 33, 500, 81, 200, 81]
  },
  {
    text: "品名：",
    char_pos: [
      [43, 107, 75, 107, 74, 140, 43, 140],  // 品
      [78, 106, 112, 106, 111, 142, 79, 141], // 名
      [121, 123, 129, 123, 128, 138, 121, 138] // ：
    ],
    pos: [40, 105, 133, 105, 133, 144, 40, 144]
  }
];

/**
 * 运行字间距计算测试
 */
export function runWordSpaceTest() {
  console.log('=== 字间距计算功能测试 ===');
  
  const results = [];
  
  testData.forEach((data, index) => {
    console.log(`\n--- 测试 ${index + 1}: "${data.text}" ---`);
    
    // 模拟不同的缩放比例
    const scaleX = 0.1; // 1像素 = 0.1毫米
    
    const result = calculator.calculateWordSpace(data.char_pos, data.text, scaleX);
    
    console.log('计算结果:', {
      文本: data.text,
      字间距像素: result.wordSpacePx,
      字间距毫米: result.wordSpaceMm,
      计算方法: result.method,
      字符数量: result.details.charCount,
      有效间距数: result.details.validGaps?.length || 0
    });
    
    // 验证计算结果
    const validation = validateResult(result, data);
    console.log('验证结果:', validation);
    
    results.push({
      index: index + 1,
      text: data.text,
      result,
      validation
    });
  });
  
  console.log('\n=== 测试总结 ===');
  const passedTests = results.filter(r => r.validation.isValid).length;
  console.log(`通过测试: ${passedTests}/${results.length}`);
  
  return results;
}

/**
 * 验证计算结果的合理性
 */
function validateResult(result, testData) {
  const validation = {
    isValid: true,
    issues: []
  };
  
  // 检查基本数据
  if (result.wordSpacePx < 0) {
    validation.isValid = false;
    validation.issues.push('字间距不能为负数');
  }
  
  if (result.wordSpaceMm < 0) {
    validation.isValid = false;
    validation.issues.push('毫米字间距不能为负数');
  }
  
  // 检查字符数量
  if (result.details.charCount !== testData.char_pos.length) {
    validation.isValid = false;
    validation.issues.push(`字符数量不匹配: 期望${testData.char_pos.length}, 实际${result.details.charCount}`);
  }
  
  // 检查计算方法
  if (!result.method) {
    validation.isValid = false;
    validation.issues.push('缺少计算方法信息');
  }
  
  // 检查合理性（字间距不应该过大）
  if (result.wordSpacePx > 50) {
    validation.issues.push('字间距可能过大（>50px）');
  }
  
  if (result.wordSpaceMm > 5) {
    validation.issues.push('字间距可能过大（>5mm）');
  }
  
  return validation;
}

/**
 * 测试不同缩放比例下的字间距计算
 */
export function testDifferentScales() {
  console.log('\n=== 不同缩放比例测试 ===');
  
  const testCase = testData[0]; // 使用第一个测试用例
  const scales = [0.05, 0.1, 0.15, 0.2]; // 不同的像素到毫米转换比例
  
  scales.forEach(scale => {
    const result = calculator.calculateWordSpace(testCase.char_pos, testCase.text, scale);
    console.log(`缩放比例 ${scale}:`, {
      字间距像素: result.wordSpacePx,
      字间距毫米: result.wordSpaceMm,
      比例验证: (result.wordSpaceMm / result.wordSpacePx).toFixed(3) + ' ≈ ' + scale
    });
  });
}

/**
 * 测试边界情况
 */
export function testEdgeCases() {
  console.log('\n=== 边界情况测试 ===');
  
  // 测试1: 只有一个字符
  console.log('测试1: 单个字符');
  const singleCharResult = calculator.calculateWordSpace(
    [[100, 100, 120, 100, 120, 120, 100, 120]], 
    '字', 
    0.1
  );
  console.log('单字符结果:', singleCharResult);
  
  // 测试2: 字符重叠
  console.log('测试2: 字符重叠');
  const overlappingResult = calculator.calculateWordSpace(
    [
      [100, 100, 120, 100, 120, 120, 100, 120], // 第一个字符
      [115, 100, 135, 100, 135, 120, 115, 120]  // 第二个字符重叠
    ],
    '重叠',
    0.1
  );
  console.log('重叠字符结果:', overlappingResult);
  
  // 测试3: 空数据
  console.log('测试3: 空数据');
  const emptyResult = calculator.calculateWordSpace([], '', 0.1);
  console.log('空数据结果:', emptyResult);
}

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数暴露到全局
  window.wordSpaceTest = {
    runWordSpaceTest,
    testDifferentScales,
    testEdgeCases
  };
  
  console.log('字间距测试函数已加载，可以在控制台中调用:');
  console.log('- window.wordSpaceTest.runWordSpaceTest()');
  console.log('- window.wordSpaceTest.testDifferentScales()');
  console.log('- window.wordSpaceTest.testEdgeCases()');
}
