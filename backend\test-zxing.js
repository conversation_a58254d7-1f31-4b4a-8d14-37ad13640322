const fs = require('fs');
const path = require('path');

/**
 * 测试ZXing库的条码解析功能
 */
async function testZXingLibrary() {
  console.log('🔍 测试ZXing库的条码解析功能...\n');

  try {
    // 1. 测试库是否正确加载
    console.log('📦 步骤1: 测试库加载...');
    const { BrowserMultiFormatReader, NotFoundException } = require('@zxing/library');
    console.log('✅ @zxing/library 加载成功');

    const reader = new BrowserMultiFormatReader();
    console.log('✅ BrowserMultiFormatReader 创建成功');

    // 2. 从调试文件中读取base64数据
    console.log('\n📄 步骤2: 读取条码base64数据...');
    const debugFile = path.join(__dirname, 'debug-textin-raw.json');
    
    if (!fs.existsSync(debugFile)) {
      throw new Error('调试文件不存在，请先运行 debug-barcode.js');
    }

    const debugData = JSON.parse(fs.readFileSync(debugFile, 'utf8'));
    const imageElements = debugData.result.pages[0].content.filter(item => item.type === 'image');
    
    if (imageElements.length === 0) {
      throw new Error('没有找到图像元素');
    }

    const barcodeElement = imageElements.find(img => img.sub_type === 'barcode');
    if (!barcodeElement) {
      throw new Error('没有找到条码元素');
    }

    const base64Data = barcodeElement.data?.base64 || barcodeElement.base64str;
    if (!base64Data) {
      throw new Error('没有找到base64数据');
    }

    console.log(`✅ 找到条码base64数据，长度: ${base64Data.length}`);

    // 3. 测试Jimp图像处理
    console.log('\n🖼️ 步骤3: 测试Jimp图像处理...');
    const Jimp = require('jimp');
    
    // 清理base64数据
    let cleanBase64 = base64Data;
    if (cleanBase64.startsWith('data:')) {
      const commaIndex = cleanBase64.indexOf(',');
      if (commaIndex !== -1) {
        cleanBase64 = cleanBase64.substring(commaIndex + 1);
      }
    }
    cleanBase64 = cleanBase64.replace(/\s/g, '');

    const imageBuffer = Buffer.from(cleanBase64, 'base64');
    console.log(`✅ 图像缓冲区创建成功，大小: ${imageBuffer.length} bytes`);

    const image = await Jimp.read(imageBuffer);
    console.log(`✅ Jimp图像读取成功，尺寸: ${image.getWidth()} x ${image.getHeight()}`);

    // 保存图像文件用于检查
    const outputImagePath = path.join(__dirname, 'debug-barcode-image.png');
    await image.writeAsync(outputImagePath);
    console.log(`✅ 图像已保存到: ${outputImagePath}`);

    // 4. 转换为ImageData格式
    console.log('\n🔄 步骤4: 转换为ImageData格式...');
    const imageData = jimpToImageData(image);
    console.log(`✅ ImageData创建成功，尺寸: ${imageData.width} x ${imageData.height}`);

    // 5. 测试ZXing解码
    console.log('\n🔍 步骤5: 测试ZXing解码...');
    
    try {
      const result = await reader.decodeFromImageData(imageData);
      
      if (result && result.getText()) {
        console.log('🎉 ZXing解码成功!');
        console.log(`📝 条码内容: "${result.getText()}"`);
        console.log(`📊 条码格式: ${result.getBarcodeFormat()}`);
        return result.getText();
      } else {
        throw new Error('解码结果为空');
      }
    } catch (zxingError) {
      console.warn('❌ ZXing解码失败:', zxingError.message);
      
      // 6. 尝试图像预处理
      console.log('\n🔧 步骤6: 尝试图像预处理...');
      const processedImage = await preprocessImage(image);
      const processedImagePath = path.join(__dirname, 'debug-barcode-processed.png');
      await processedImage.writeAsync(processedImagePath);
      console.log(`✅ 预处理图像已保存到: ${processedImagePath}`);

      const processedImageData = jimpToImageData(processedImage);
      console.log(`✅ 预处理ImageData创建成功，尺寸: ${processedImageData.width} x ${processedImageData.height}`);

      try {
        const processedResult = await reader.decodeFromImageData(processedImageData);
        
        if (processedResult && processedResult.getText()) {
          console.log('🎉 预处理后ZXing解码成功!');
          console.log(`📝 条码内容: "${processedResult.getText()}"`);
          console.log(`📊 条码格式: ${processedResult.getBarcodeFormat()}`);
          return processedResult.getText();
        } else {
          throw new Error('预处理后解码结果仍为空');
        }
      } catch (processedError) {
        console.warn('❌ 预处理后ZXing解码也失败:', processedError.message);
        throw new Error('所有解码方法都失败了');
      }
    }

  } catch (error) {
    console.error('💥 测试失败:', error.message);
    console.error('错误详情:', error);
    
    // 提供解决建议
    console.log('\n💡 可能的解决方案:');
    console.log('1. 检查@zxing/library是否正确安装: npm list @zxing/library');
    console.log('2. 尝试重新安装: npm uninstall @zxing/library && npm install @zxing/library');
    console.log('3. 检查条码图像质量是否足够清晰');
    console.log('4. 考虑使用其他条码解析库');
  }
}

/**
 * 将Jimp图像转换为ImageData格式
 */
function jimpToImageData(image) {
  const Jimp = require('jimp');
  const width = image.getWidth();
  const height = image.getHeight();
  const data = new Uint8ClampedArray(width * height * 4);

  let idx = 0;
  image.scan(0, 0, width, height, (x, y, idx2) => {
    const pixel = Jimp.intToRGBA(image.getPixelColor(x, y));
    data[idx++] = pixel.r;
    data[idx++] = pixel.g;
    data[idx++] = pixel.b;
    data[idx++] = pixel.a;
  });

  return {
    data: data,
    width: width,
    height: height
  };
}

/**
 * 图像预处理
 */
async function preprocessImage(image) {
  // 转换为灰度图
  let processed = image.clone().greyscale();
  
  // 增强对比度
  processed = processed.contrast(0.5);
  
  // 调整亮度
  processed = processed.brightness(0.1);
  
  // 如果图像太小，进行放大
  if (processed.getWidth() < 200 || processed.getHeight() < 200) {
    const scale = Math.max(200 / processed.getWidth(), 200 / processed.getHeight());
    processed = processed.scale(scale, Jimp.RESIZE_NEAREST_NEIGHBOR);
  }

  return processed;
}

// 执行测试
testZXingLibrary().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试过程中发生错误:', error.message);
});
