/**
 * WordSpaceCalculator 单元测试
 */

import { WordSpaceCalculator } from '../wordSpaceCalculator';
import { calculateCharBounds } from '../charPosAnalyzer';

describe('WordSpaceCalculator', () => {
  let calculator;

  beforeEach(() => {
    calculator = new WordSpaceCalculator();
    calculator.setDebugMode(false); // 关闭调试模式以减少测试输出
  });

  describe('基本功能测试', () => {
    test('应该正确计算正常字符间距', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120], // 第一个字符 20px宽
        [130, 100, 150, 100, 150, 120, 130, 120]  // 第二个字符 20px宽，间距10px
      ];
      const text = '测试';
      const scaleX = 0.1; // 1px = 0.1mm

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      expect(result.wordSpacePx).toBe(10); // 130 - 120 = 10px
      expect(result.wordSpaceMm).toBe(1);  // 10px * 0.1 = 1mm
      expect(result.method).toBe('non_overlapping_average');
      expect(result.details.charCount).toBe(2);
      expect(result.details.validGaps).toHaveLength(1);
    });

    test('应该处理多个字符的平均间距', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120], // 字符1: 20px宽
        [130, 100, 150, 100, 150, 120, 130, 120], // 字符2: 20px宽，间距10px
        [165, 100, 185, 100, 185, 120, 165, 120]  // 字符3: 20px宽，间距15px
      ];
      const text = '测试字';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      expect(result.wordSpacePx).toBe(12.5); // (10 + 15) / 2 = 12.5px
      expect(result.wordSpaceMm).toBe(1.25); // 12.5px * 0.1 = 1.25mm
      expect(result.details.validGaps).toHaveLength(2);
    });
  });

  describe('边界情况测试', () => {
    test('应该处理单个字符（无间距）', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120]
      ];
      const text = '单';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      expect(result.wordSpacePx).toBe(0);
      expect(result.wordSpaceMm).toBe(0);
      expect(result.method).toBe('no_chars');
      expect(result.details.gaps).toHaveLength(0);
    });

    test('应该处理字符重叠情况', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120], // 字符1: 100-120
        [115, 100, 135, 100, 135, 120, 115, 120]  // 字符2: 115-135，重叠5px
      ];
      const text = '重叠';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      expect(result.wordSpacePx).toBe(0); // 重叠情况下间距为0
      expect(result.wordSpaceMm).toBe(0);
      expect(result.method).toBe('all_overlapping');
      expect(result.details.validGaps[0].isOverlapping).toBe(true);
      expect(result.details.validGaps[0].overlap).toBe(5);
    });

    test('应该处理空数据', () => {
      const result = calculator.calculateWordSpace([], '', 0.1);

      expect(result.wordSpacePx).toBe(0);
      expect(result.wordSpaceMm).toBe(0);
      expect(result.method).toBe('no_chars');
      expect(result.details.charCount).toBe(0);
    });

    test('应该处理无效的char_pos数据', () => {
      const charPos = [
        [100, 100, 120], // 无效数据：少于8个元素
        [130, 100, 150, 100, 150, 120, 130, 120]
      ];
      const text = '无效';
      const scaleX = 0.1;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      // 应该能处理无效数据而不崩溃
      expect(result).toBeDefined();
      expect(result.wordSpacePx).toBeGreaterThanOrEqual(0);
    });
  });

  describe('单位转换测试', () => {
    test('应该正确转换不同的缩放比例', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120],
        [130, 100, 150, 100, 150, 120, 130, 120]
      ];
      const text = '转换';

      // 测试不同的缩放比例
      const scales = [0.05, 0.1, 0.2, 0.5];
      const expectedPixels = 10; // 固定的像素间距

      scales.forEach(scale => {
        const result = calculator.calculateWordSpace(charPos, text, scale);
        expect(result.wordSpacePx).toBe(expectedPixels);
        expect(result.wordSpaceMm).toBeCloseTo(expectedPixels * scale, 3);
      });
    });

    test('应该处理零缩放比例', () => {
      const charPos = [
        [100, 100, 120, 100, 120, 120, 100, 120],
        [130, 100, 150, 100, 150, 120, 130, 120]
      ];
      const text = '零比例';
      const scaleX = 0;

      const result = calculator.calculateWordSpace(charPos, text, scaleX);

      expect(result.wordSpacePx).toBe(10);
      expect(result.wordSpaceMm).toBe(0);
    });
  });

  describe('字体大小调整测试', () => {
    test('应该根据字体大小调整字间距', () => {
      const baseWordSpace = 10;
      const referenceFontSize = 12;

      // 测试不同字体大小
      expect(calculator.adjustWordSpaceByFontSize(baseWordSpace, 12, referenceFontSize)).toBe(10);
      expect(calculator.adjustWordSpaceByFontSize(baseWordSpace, 24, referenceFontSize)).toBe(20);
      expect(calculator.adjustWordSpaceByFontSize(baseWordSpace, 6, referenceFontSize)).toBe(5);
    });

    test('应该处理无效字体大小', () => {
      const baseWordSpace = 10;
      
      expect(calculator.adjustWordSpaceByFontSize(baseWordSpace, 0)).toBe(10);
      expect(calculator.adjustWordSpaceByFontSize(baseWordSpace, -5)).toBe(10);
      expect(calculator.adjustWordSpaceByFontSize(baseWordSpace, null)).toBe(10);
    });
  });

  describe('验证功能测试', () => {
    test('应该验证合理的字间距', () => {
      const wordSpace = 5;
      const averageCharWidth = 20;

      const validation = calculator.validateWordSpace(wordSpace, averageCharWidth);

      expect(validation.isReasonable).toBe(true);
      expect(validation.ratio).toBe(0.25); // 5/20 = 0.25
      expect(validation.quality).toBe('good');
    });

    test('应该检测过大的字间距', () => {
      const wordSpace = 15;
      const averageCharWidth = 20;

      const validation = calculator.validateWordSpace(wordSpace, averageCharWidth);

      expect(validation.isReasonable).toBe(false);
      expect(validation.quality).toBe('too_large');
      expect(validation.recommendation).toContain('过大');
    });

    test('应该检测负数字间距', () => {
      const wordSpace = -5;
      const averageCharWidth = 20;

      const validation = calculator.validateWordSpace(wordSpace, averageCharWidth);

      expect(validation.isReasonable).toBe(false);
      expect(validation.quality).toBe('negative');
      expect(validation.recommendation).toContain('负值');
    });
  });

  describe('批量计算测试', () => {
    test('应该正确处理多行文本', () => {
      const textLines = [
        {
          text: '第一行',
          char_pos: [
            [100, 100, 120, 100, 120, 120, 100, 120],
            [130, 100, 150, 100, 150, 120, 130, 120],
            [160, 100, 180, 100, 180, 120, 160, 120]
          ]
        },
        {
          text: '第二行',
          char_pos: [
            [100, 150, 120, 150, 120, 170, 100, 170],
            [125, 150, 145, 150, 145, 170, 125, 170],
            [155, 150, 175, 150, 175, 170, 155, 170]
          ]
        }
      ];
      const scaleX = 0.1;

      const results = calculator.calculateMultipleWordSpaces(textLines, scaleX);

      expect(results).toHaveLength(2);
      expect(results[0].text).toBe('第一行');
      expect(results[1].text).toBe('第二行');
      expect(results[0].wordSpacePx).toBe(10); // 第一行间距
      expect(results[1].wordSpacePx).toBe(7.5); // 第二行间距 (5+10)/2
    });
  });
});

describe('calculateCharBounds', () => {
  test('应该正确计算字符边界框', () => {
    const charPos = [100, 50, 120, 50, 120, 70, 100, 70];
    const bounds = calculateCharBounds(charPos);

    expect(bounds.x).toBe(100);
    expect(bounds.y).toBe(50);
    expect(bounds.width).toBe(20);
    expect(bounds.height).toBe(20);
  });

  test('应该处理无效的char_pos数据', () => {
    const bounds = calculateCharBounds([]);
    
    expect(bounds.x).toBe(0);
    expect(bounds.y).toBe(0);
    expect(bounds.width).toBe(0);
    expect(bounds.height).toBe(0);
  });

  test('应该处理不规则四边形', () => {
    // 倾斜的四边形
    const charPos = [100, 50, 120, 55, 115, 75, 95, 70];
    const bounds = calculateCharBounds(charPos);

    expect(bounds.x).toBe(95);  // min x
    expect(bounds.y).toBe(50);  // min y
    expect(bounds.width).toBe(25); // 120 - 95
    expect(bounds.height).toBe(25); // 75 - 50
  });
});
