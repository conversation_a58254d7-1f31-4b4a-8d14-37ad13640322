/**
 * 字间距计算工具
 * 基于TextIn API的char_pos数据计算准确的字间距
 */

import { calculateCharBounds } from './charPosAnalyzer';

/**
 * 字间距计算器类
 */
export class WordSpaceCalculator {
  constructor() {
    this.debugMode = true; // 开启调试模式
  }

  /**
   * 计算文本行的字间距
   * @param {Array} charPosArray - char_pos数组
   * @param {string} text - 文本内容
   * @param {number} scaleX - X轴像素到毫米的转换比例
   * @returns {Object} 计算结果
   */
  calculateWordSpace(charPosArray, text, scaleX = 1) {
    if (!charPosArray || charPosArray.length < 2) {
      return {
        wordSpaceMm: 0,
        wordSpacePx: 0,
        method: 'no_chars',
        details: {
          charCount: charPosArray?.length || 0,
          gaps: [],
          averageGap: 0,
          totalGap: 0
        }
      };
    }

    // 计算所有字符的边界框
    const characters = charPosArray.map((charPos, index) => ({
      char: text[index] || '',
      index,
      bounds: calculateCharBounds(charPos),
      charPos
    }));

    // 计算字符间的间距
    const gaps = [];
    for (let i = 0; i < characters.length - 1; i++) {
      const currentChar = characters[i];
      const nextChar = characters[i + 1];
      
      const gap = this.calculateCharacterGap(currentChar, nextChar);
      gaps.push(gap);
    }

    // 过滤和处理间距数据
    const processedResult = this.processGaps(gaps);
    
    // 转换为毫米
    const wordSpacePx = processedResult.averageGap;
    const wordSpaceMm = wordSpacePx * scaleX;

    const result = {
      wordSpaceMm: Math.round(wordSpaceMm * 1000) / 1000, // 保留3位小数
      wordSpacePx: Math.round(wordSpacePx * 100) / 100,   // 保留2位小数
      method: processedResult.method,
      details: {
        charCount: characters.length,
        gaps: gaps,
        validGaps: processedResult.validGaps,
        averageGap: processedResult.averageGap,
        totalGap: processedResult.totalGap,
        scaleX: scaleX
      }
    };

    if (this.debugMode) {
      console.log(`字间距计算结果 - 文本: "${text}":`, result);
    }

    return result;
  }

  /**
   * 计算两个字符之间的间距
   * @param {Object} char1 - 第一个字符
   * @param {Object} char2 - 第二个字符
   * @returns {Object} 间距信息
   */
  calculateCharacterGap(char1, char2) {
    const char1Right = char1.bounds.x + char1.bounds.width;
    const char2Left = char2.bounds.x;
    
    const rawGap = char2Left - char1Right;
    const isOverlapping = rawGap < 0;
    const overlap = isOverlapping ? Math.abs(rawGap) : 0;
    const effectiveGap = Math.max(0, rawGap);

    return {
      char1: char1.char,
      char2: char2.char,
      rawGap,
      effectiveGap,
      overlap,
      isOverlapping,
      char1Right,
      char2Left,
      char1Width: char1.bounds.width,
      char2Width: char2.bounds.width
    };
  }

  /**
   * 处理间距数据，计算平均值
   * @param {Array} gaps - 间距数组
   * @returns {Object} 处理结果
   */
  processGaps(gaps) {
    if (gaps.length === 0) {
      return {
        averageGap: 0,
        totalGap: 0,
        validGaps: [],
        method: 'no_gaps'
      };
    }

    // 策略1: 使用所有非重叠的间距
    const nonOverlappingGaps = gaps.filter(gap => !gap.isOverlapping);
    
    if (nonOverlappingGaps.length > 0) {
      const totalGap = nonOverlappingGaps.reduce((sum, gap) => sum + gap.effectiveGap, 0);
      const averageGap = totalGap / nonOverlappingGaps.length;
      
      return {
        averageGap,
        totalGap,
        validGaps: nonOverlappingGaps,
        method: 'non_overlapping_average'
      };
    }

    // 策略2: 如果所有字符都重叠，使用最小重叠的间距
    const minOverlapGap = gaps.reduce((min, gap) => 
      gap.overlap < min.overlap ? gap : min
    );

    return {
      averageGap: 0, // 重叠情况下字间距为0
      totalGap: 0,
      validGaps: [minOverlapGap],
      method: 'all_overlapping'
    };
  }

  /**
   * 基于字体大小调整字间距
   * @param {number} baseWordSpace - 基础字间距
   * @param {number} fontSize - 字体大小
   * @param {number} referenceFontSize - 参考字体大小（默认12）
   * @returns {number} 调整后的字间距
   */
  adjustWordSpaceByFontSize(baseWordSpace, fontSize, referenceFontSize = 12) {
    if (!fontSize || fontSize <= 0) {
      return baseWordSpace;
    }

    // 字间距与字体大小成正比
    const scaleFactor = fontSize / referenceFontSize;
    return baseWordSpace * scaleFactor;
  }

  /**
   * 验证字间距计算的合理性
   * @param {number} wordSpace - 计算出的字间距
   * @param {number} averageCharWidth - 平均字符宽度
   * @returns {Object} 验证结果
   */
  validateWordSpace(wordSpace, averageCharWidth) {
    const ratio = averageCharWidth > 0 ? wordSpace / averageCharWidth : 0;
    
    // 合理的字间距通常是字符宽度的0-50%
    const isReasonable = ratio >= 0 && ratio <= 0.5;
    
    let quality = 'good';
    if (ratio > 0.5) {
      quality = 'too_large';
    } else if (ratio < 0) {
      quality = 'negative';
    } else if (ratio === 0) {
      quality = 'zero';
    }

    return {
      isReasonable,
      ratio: Math.round(ratio * 1000) / 1000,
      quality,
      recommendation: this.getRecommendation(quality, ratio)
    };
  }

  /**
   * 获取字间距优化建议
   * @param {string} quality - 质量评级
   * @param {number} ratio - 字间距与字符宽度的比例
   * @returns {string} 建议
   */
  getRecommendation(quality, ratio) {
    switch (quality) {
      case 'too_large':
        return `字间距过大(${(ratio * 100).toFixed(1)}%)，建议检查字符识别准确性`;
      case 'negative':
        return '字间距为负值，字符可能重叠，建议使用0或小正值';
      case 'zero':
        return '字间距为0，字符紧密排列，这在某些字体中是正常的';
      case 'good':
      default:
        return `字间距合理(${(ratio * 100).toFixed(1)}%)`;
    }
  }

  /**
   * 批量计算多行文本的字间距
   * @param {Array} textLines - 文本行数组，每个包含{char_pos, text}
   * @param {number} scaleX - 转换比例
   * @returns {Array} 计算结果数组
   */
  calculateMultipleWordSpaces(textLines, scaleX = 1) {
    return textLines.map((line, index) => {
      const result = this.calculateWordSpace(line.char_pos, line.text, scaleX);
      
      return {
        lineIndex: index,
        text: line.text,
        ...result
      };
    });
  }

  /**
   * 设置调试模式
   * @param {boolean} enabled - 是否启用调试
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
  }
}

// 创建默认实例
export const wordSpaceCalculator = new WordSpaceCalculator();

// 便捷函数
export function calculateWordSpace(charPosArray, text, scaleX = 1) {
  return wordSpaceCalculator.calculateWordSpace(charPosArray, text, scaleX);
}

export function calculateMultipleWordSpaces(textLines, scaleX = 1) {
  return wordSpaceCalculator.calculateMultipleWordSpaces(textLines, scaleX);
}
