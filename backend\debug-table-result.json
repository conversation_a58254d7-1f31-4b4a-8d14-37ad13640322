{"success": true, "data": {"imageInfo": {"width": 800, "height": 600, "format": "png"}, "elements": [{"elementType": "1", "x": 212, "y": 42, "width": 788, "height": 71, "content": "扁桃仁开心果牛奶巧克力", "charWidth": 56.36, "bold": true, "italic": false, "angle": 0}, {"elementType": "1", "x": 40, "y": 137, "width": 1124, "height": 179, "content": "配料：白砂储、可可快、可可，被物法、脱腻奶粉、乳，清粉、开心果奶油《白砂糖、横物法、脱思奶粉、开心果、乳清制）。房桃仁奶油《白矽德、植物法、脱奶粉、腐桃仁、乳，清标）。食物添加剂《模测、食用香F）。可可含量不低于31％", "charWidth": 10.31, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 41, "y": 327, "width": 248, "height": 36, "content": "原产国：意大利", "charWidth": 34.83, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 41, "y": 374, "width": 435, "height": 36, "content": "生厂日期：2022年01月12日", "charWidth": 31.86, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 40, "y": 421, "width": 687, "height": 36, "content": "贮存方式：置放闹凉干燥处，避免阳光直酒", "charWidth": 35.47, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 41, "y": 471, "width": 436, "height": 36, "content": "保质期至：2023年01月23日", "charWidth": 33.14, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 41, "y": 518, "width": 193, "height": 41, "content": "生产商：xxx", "charWidth": 35, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 41, "y": 565, "width": 306, "height": 36, "content": "国内总经销商：xxx", "charWidth": 34.33, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 40, "y": 612, "width": 155, "height": 36, "content": "地址：xxx", "charWidth": 36, "bold": false, "italic": false, "angle": 0}, {"elementType": "10", "x": 520, "y": 465, "width": 639, "height": 342, "rows": 6, "cols": 3, "columnWidths": [213, 213, 213], "rowHeights": [57, 57, 57, 57, 57, 57], "cells": [{"row": 0, "col": 0, "content": "营养成分表", "charWidth": 10, "bold": false, "italic": false}, {"row": 1, "col": 0, "content": "项目", "charWidth": 25, "bold": false, "italic": false}, {"row": 1, "col": 1, "content": "每100g ", "charWidth": 8.33, "bold": false, "italic": false}, {"row": 1, "col": 2, "content": "NRV%", "charWidth": 12.5, "bold": false, "italic": false}, {"row": 2, "col": 0, "content": "能量", "charWidth": 25, "bold": false, "italic": false}, {"row": 2, "col": 1, "content": "912kJ ", "charWidth": 8.33, "bold": false, "italic": false}, {"row": 2, "col": 2, "content": "11%", "charWidth": 16.67, "bold": false, "italic": false}, {"row": 3, "col": 0, "content": "蛋白质", "charWidth": 16.67, "bold": false, "italic": false}, {"row": 3, "col": 1, "content": "7.4g ", "charWidth": 10, "bold": false, "italic": false}, {"row": 3, "col": 2, "content": "12%", "charWidth": 16.67, "bold": false, "italic": false}, {"row": 4, "col": 0, "content": "脂肪<br>碳水化合物", "charWidth": 8, "bold": false, "italic": false}, {"row": 4, "col": 1, "content": "4.8g<br>35.8g ", "charWidth": 8, "bold": false, "italic": false}, {"row": 4, "col": 2, "content": "8%", "charWidth": 25, "bold": false, "italic": false}, {"row": 5, "col": 2, "content": "12%", "charWidth": 16.67, "bold": false, "italic": false}]}, {"elementType": "1", "x": 40, "y": 659, "width": 136, "height": 38, "content": "电话：xx", "charWidth": 34.5, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 41, "y": 708, "width": 135, "height": 35, "content": "传真：xx", "charWidth": 36, "bold": false, "italic": false, "angle": 0}, {"elementType": "1", "x": 40, "y": 755, "width": 211, "height": 35, "content": "净重量120克", "charWidth": 34.5, "bold": false, "italic": false, "angle": 0}]}}