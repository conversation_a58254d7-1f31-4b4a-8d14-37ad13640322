const { BrowserMultiFormatReader, NotFoundException } = require('@zxing/library');
const Jimp = require('jimp');

/**
 * 条码解码服务
 * 用于解析条码和二维码的内容
 */
class BarcodeDecoder {
  constructor() {
    this.reader = new BrowserMultiFormatReader();
  }

  /**
   * 解码条码/二维码
   * @param {string} base64Data - base64图像数据
   * @param {string} type - 类型 ('barcode' 或 'qrcode')
   * @returns {Promise<string>} 解析出的内容
   */
  async decode(base64Data, type = 'barcode') {
    try {
      console.log(`开始解析${type}...`);

      // 清理base64数据
      const cleanBase64 = this.cleanBase64Data(base64Data);
      
      // 将base64转换为图像
      const imageBuffer = Buffer.from(cleanBase64, 'base64');
      const image = await Jimp.read(imageBuffer);
      
      // 转换为ImageData格式
      const imageData = this.jimpToImageData(image);
      
      // 使用ZXing解码
      const result = await this.reader.decodeFromImageData(imageData);
      
      if (result && result.getText()) {
        const content = result.getText();
        console.log(`${type}解析成功:`, content);
        return content;
      } else {
        throw new Error('解析结果为空');
      }

    } catch (error) {
      console.warn(`${type}解析失败:`, error.message);
      
      // 如果ZXing解析失败，尝试其他方法
      if (error instanceof NotFoundException) {
        console.log('ZXing未找到条码，尝试其他方法...');
        return await this.fallbackDecode(base64Data, type);
      }
      
      throw error;
    }
  }

  /**
   * 清理base64数据
   * @param {string} base64Data - 原始base64数据
   * @returns {string} 清理后的base64数据
   */
  cleanBase64Data(base64Data) {
    if (!base64Data) {
      throw new Error('base64数据为空');
    }

    // 移除data URL前缀
    let cleanData = base64Data;
    if (cleanData.startsWith('data:')) {
      const commaIndex = cleanData.indexOf(',');
      if (commaIndex !== -1) {
        cleanData = cleanData.substring(commaIndex + 1);
      }
    }

    // 移除空白字符
    cleanData = cleanData.replace(/\s/g, '');
    
    return cleanData;
  }

  /**
   * 将Jimp图像转换为ImageData格式
   * @param {Jimp} image - Jimp图像对象
   * @returns {ImageData} ImageData对象
   */
  jimpToImageData(image) {
    const width = image.bitmap.width;
    const height = image.bitmap.height;
    const data = new Uint8ClampedArray(width * height * 4);

    let idx = 0;
    image.scan(0, 0, width, height, (x, y, idx2) => {
      const pixel = Jimp.intToRGBA(image.getPixelColor(x, y));
      data[idx++] = pixel.r;
      data[idx++] = pixel.g;
      data[idx++] = pixel.b;
      data[idx++] = pixel.a;
    });

    return {
      data: data,
      width: width,
      height: height
    };
  }

  /**
   * 备用解码方法
   * @param {string} base64Data - base64图像数据
   * @param {string} type - 类型
   * @returns {Promise<string>} 解析结果
   */
  async fallbackDecode(base64Data, type) {
    try {
      // 尝试图像预处理后再解码
      const processedImage = await this.preprocessImage(base64Data);
      const imageData = this.jimpToImageData(processedImage);
      
      const result = await this.reader.decodeFromImageData(imageData);
      
      if (result && result.getText()) {
        console.log(`${type}预处理后解析成功:`, result.getText());
        return result.getText();
      }
      
      throw new Error('预处理后仍无法解析');
      
    } catch (error) {
      console.warn(`${type}备用解码也失败:`, error.message);
      throw new Error(`无法解析${type}内容`);
    }
  }

  /**
   * 图像预处理
   * @param {string} base64Data - base64图像数据
   * @returns {Promise<Jimp>} 处理后的图像
   */
  async preprocessImage(base64Data) {
    const cleanBase64 = this.cleanBase64Data(base64Data);
    const imageBuffer = Buffer.from(cleanBase64, 'base64');
    let image = await Jimp.read(imageBuffer);

    // 转换为灰度图
    image = image.greyscale();
    
    // 增强对比度
    image = image.contrast(0.5);
    
    // 调整亮度
    image = image.brightness(0.1);
    
    // 如果图像太小，进行放大
    if (image.bitmap.width < 200 || image.bitmap.height < 200) {
      const scale = Math.max(200 / image.bitmap.width, 200 / image.bitmap.height);
      image = image.scale(scale, Jimp.RESIZE_NEAREST_NEIGHBOR);
    }

    return image;
  }

  /**
   * 批量解码多个条码
   * @param {Array} base64Array - base64数据数组
   * @param {string} type - 类型
   * @returns {Promise<Array>} 解析结果数组
   */
  async decodeBatch(base64Array, type = 'barcode') {
    const results = [];
    
    for (let i = 0; i < base64Array.length; i++) {
      try {
        const content = await this.decode(base64Array[i], type);
        results.push({
          index: i,
          success: true,
          content: content
        });
      } catch (error) {
        results.push({
          index: i,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 验证条码格式
   * @param {string} content - 条码内容
   * @param {string} expectedFormat - 期望的格式
   * @returns {boolean} 是否符合格式
   */
  validateBarcodeFormat(content, expectedFormat) {
    if (!content) return false;

    switch (expectedFormat.toUpperCase()) {
      case 'EAN13':
        return /^\d{13}$/.test(content);
      case 'EAN8':
        return /^\d{8}$/.test(content);
      case 'UPC_A':
        return /^\d{12}$/.test(content);
      case 'CODE128':
        return content.length > 0; // CODE128可以包含任意字符
      case 'CODE39':
        return /^[A-Z0-9\-. $\/+%]+$/.test(content);
      default:
        return true; // 未知格式，默认通过
    }
  }

  /**
   * 获取条码类型
   * @param {string} content - 条码内容
   * @returns {string} 条码类型
   */
  detectBarcodeType(content) {
    if (!content) return 'UNKNOWN';

    if (/^\d{13}$/.test(content)) return 'EAN13';
    if (/^\d{8}$/.test(content)) return 'EAN8';
    if (/^\d{12}$/.test(content)) return 'UPC_A';
    if (/^[A-Z0-9\-. $\/+%]+$/.test(content)) return 'CODE39';
    
    return 'CODE128'; // 默认类型
  }

  /**
   * 清理资源
   */
  dispose() {
    if (this.reader) {
      try {
        this.reader.reset();
      } catch (error) {
        console.warn('清理条码读取器失败:', error.message);
      }
    }
  }
}

module.exports = BarcodeDecoder;
