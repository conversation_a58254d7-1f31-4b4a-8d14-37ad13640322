{"name": "image-recognition-backend", "version": "1.0.0", "description": "图片识别后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["image-recognition", "textin", "api", "backend"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "axios": "^1.6.0", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "sharp": "^0.32.6", "@zxing/library": "^0.20.0", "jimp": "^0.22.10"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}