const fs = require('fs');
const path = require('path');

// 加载环境变量
require('dotenv').config();

const TextInService = require('./services/textinService');
const DataConverter = require('./services/dataConverter');

/**
 * 测试鞋子图片的字符宽度问题
 */
async function testXieziImage() {
  console.log('🔍 测试鞋子图片的字符宽度问题...\n');

  const imagePath = path.join(__dirname, 'data', '鞋子（60_40）3.png');
  
  if (!fs.existsSync(imagePath)) {
    console.error('❌ 测试图片不存在:', imagePath);
    return;
  }

  console.log('📷 测试图片:', imagePath);
  
  try {
    // 1. 直接调用TextIn API
    console.log('\n🔧 步骤1: 调用TextIn API...');
    const textinService = new TextInService();
    const textinResult = await textinService.recognizeFile(imagePath);
    
    // 保存原始响应
    const rawOutputPath = path.join(__dirname, 'debug-xiezi-raw.json');
    fs.writeFileSync(rawOutputPath, JSON.stringify(textinResult, null, 2));
    console.log(`💾 原始TextIn响应已保存到: ${rawOutputPath}`);
    
    // 2. 分析表格中的单元格数据
    console.log('\n🔍 步骤2: 分析表格中的单元格数据...');
    const structured = textinResult.result?.structured || [];
    const tables = structured.filter(item => item.type === 'table');
    
    if (tables.length > 0) {
      const table = tables[0];
      console.log(`找到表格，包含 ${table.cells?.length || 0} 个单元格:`);
      
      // 重点分析"米灰桔"和"牛皮"的数据
      const targetCells = table.cells.filter(cell => 
        cell.text === '米灰桔' || cell.text === '牛皮'
      );
      
      console.log('\n🎯 重点分析目标单元格:');
      targetCells.forEach(cell => {
        console.log(`\n单元格: "${cell.text}"`);
        console.log(`- 位置: [${cell.row}, ${cell.col}]`);
        console.log(`- 坐标: [${cell.pos?.join(', ') || '无'}]`);
        console.log(`- 字符位置数据: ${cell.char_pos ? '有' : '无'}`);
        
        if (cell.char_pos && cell.char_pos.length > 0) {
          console.log(`- 字符数量: ${cell.char_pos.length}`);
          console.log(`- 字符详情:`);
          cell.char_pos.forEach((char, index) => {
            const width = char.pos[2] - char.pos[0];
            const height = char.pos[3] - char.pos[1];
            console.log(`  ${index + 1}. "${char.char}": 宽度=${width}px, 高度=${height}px, 位置=[${char.pos.join(', ')}]`);
          });
          
          // 计算平均字符宽度
          const totalWidth = cell.char_pos.reduce((sum, char) => sum + (char.pos[2] - char.pos[0]), 0);
          const avgWidth = totalWidth / cell.char_pos.length;
          console.log(`- 平均字符宽度: ${avgWidth.toFixed(2)}px`);
        }
      });
    }
    
    // 3. 转换为APP格式并分析
    console.log('\n🔄 步骤3: 转换为APP格式...');
    const dataConverter = new DataConverter();
    const imageInfo = { width: 720, height: 480, format: 'png' };
    const elements = await dataConverter.convertToAppFormat(textinResult, imageInfo);
    
    // 4. 分析转换后的字符宽度
    console.log('\n📊 转换后的字符宽度分析:');
    const tableElement = elements.find(el => el.elementType === '10');
    
    if (tableElement) {
      const targetCells = tableElement.cells.filter(cell => 
        cell.content === '米灰桔' || cell.content === '牛皮'
      );
      
      console.log('\n🎯 目标单元格的转换结果:');
      targetCells.forEach(cell => {
        console.log(`\n单元格: "${cell.content}"`);
        console.log(`- 位置: [${cell.row}, ${cell.col}]`);
        console.log(`- 转换后字符宽度: ${cell.charWidth}px`);
        console.log(`- 字符数量: ${cell.content.length}`);
        console.log(`- 每字符平均宽度: ${(cell.charWidth * cell.content.length).toFixed(2)}px`);
      });
      
      // 比较分析
      if (targetCells.length === 2) {
        const cell1 = targetCells[0];
        const cell2 = targetCells[1];
        const ratio = cell1.charWidth / cell2.charWidth;
        console.log(`\n📏 字符宽度比较:`);
        console.log(`"${cell1.content}" vs "${cell2.content}"`);
        console.log(`${cell1.charWidth}px vs ${cell2.charWidth}px`);
        console.log(`比例: ${ratio.toFixed(2)}:1`);
        
        if (Math.abs(ratio - 1) > 0.1) {
          console.log(`⚠️  字符宽度差异较大，可能存在问题`);
        } else {
          console.log(`✅ 字符宽度基本一致`);
        }
      }
    }

    // 保存转换结果
    const resultOutputPath = path.join(__dirname, 'debug-xiezi-result.json');
    fs.writeFileSync(resultOutputPath, JSON.stringify({
      success: true,
      data: {
        imageInfo: imageInfo,
        elements: elements
      }
    }, null, 2));
    console.log(`\n💾 转换结果已保存到: ${resultOutputPath}`);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 执行测试
testXieziImage().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
