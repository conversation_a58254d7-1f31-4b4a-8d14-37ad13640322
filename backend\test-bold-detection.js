/**
 * 测试粗体检测功能
 */

const DataConverter = require('./services/dataConverter');

console.log('🔍 测试粗体检测功能...\n');

// 创建数据转换器实例
const dataConverter = new DataConverter();

// 测试用例
const testCases = [
  {
    input: '**解冻标签**',
    expected: { cleanText: '解冻标签', bold: true, italic: false }
  },
  {
    input: '**粗体文本**',
    expected: { cleanText: '粗体文本', bold: true, italic: false }
  },
  {
    input: '*斜体文本*',
    expected: { cleanText: '斜体文本', bold: false, italic: true }
  },
  {
    input: '普通文本',
    expected: { cleanText: '普通文本', bold: false, italic: false }
  },
  {
    input: '**',
    expected: { cleanText: '**', bold: false, italic: false }
  },
  {
    input: '**不完整',
    expected: { cleanText: '**不完整', bold: false, italic: false }
  },
  {
    input: '不完整**',
    expected: { cleanText: '不完整**', bold: false, italic: false }
  },
  {
    input: '*单个星号*',
    expected: { cleanText: '单个星号', bold: false, italic: true }
  },
  {
    input: '*',
    expected: { cleanText: '*', bold: false, italic: false }
  },
  {
    input: '',
    expected: { cleanText: '', bold: false, italic: false }
  },
  {
    input: '**中文粗体测试**',
    expected: { cleanText: '中文粗体测试', bold: true, italic: false }
  },
  {
    input: '**English Bold**',
    expected: { cleanText: 'English Bold', bold: true, italic: false }
  },
  {
    input: '**数字123**',
    expected: { cleanText: '数字123', bold: true, italic: false }
  },
  {
    input: '**符号!@#$%**',
    expected: { cleanText: '符号!@#$%', bold: true, italic: false }
  }
];

console.log('📋 测试用例:');
console.log('='.repeat(80));

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  const result = dataConverter.detectTextStyle(testCase.input);
  const passed = 
    result.cleanText === testCase.expected.cleanText &&
    result.bold === testCase.expected.bold &&
    result.italic === testCase.expected.italic;

  console.log(`\n测试 ${index + 1}: ${passed ? '✅ 通过' : '❌ 失败'}`);
  console.log(`输入: "${testCase.input}"`);
  console.log(`期望: cleanText="${testCase.expected.cleanText}", bold=${testCase.expected.bold}, italic=${testCase.expected.italic}`);
  console.log(`实际: cleanText="${result.cleanText}", bold=${result.bold}, italic=${result.italic}`);

  if (passed) {
    passedTests++;
  } else {
    console.log(`❌ 不匹配!`);
  }
});

console.log('\n' + '='.repeat(80));
console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);

if (passedTests === totalTests) {
  console.log('🎉 所有测试通过!');
} else {
  console.log(`⚠️  有 ${totalTests - passedTests} 个测试失败`);
}

// 测试实际的TextIn数据格式
console.log('\n🔧 测试实际TextIn数据格式...');

const mockTextInData = {
  result: {
    pages: [{
      content: [
        {
          id: 1,
          type: 'line',
          text: '**解冻标签**',
          pos: [100, 100, 200, 100, 200, 130, 100, 130],
          char_pos: []
        },
        {
          id: 2,
          type: 'line',
          text: '普通文本',
          pos: [100, 150, 200, 150, 200, 180, 100, 180],
          char_pos: []
        }
      ]
    }],
    structured: [
      {
        type: 'paragraph',
        content: [1],
        text: '**解冻标签**',
        markdown: '**解冻标签**'
      },
      {
        type: 'paragraph',
        content: [2],
        text: '普通文本',
        markdown: '普通文本'
      }
    ]
  }
};

const mockImageInfo = { width: 400, height: 300, format: 'png' };

console.log('\n转换TextIn数据...');
dataConverter.convertToAppFormat(mockTextInData, mockImageInfo).then(elements => {
  console.log('\n📋 转换结果:');
  elements.forEach((element, index) => {
    console.log(`\n元素 ${index + 1}:`);
    console.log(`- 内容: "${element.content}"`);
    console.log(`- 粗体: ${element.bold}`);
    console.log(`- 斜体: ${element.italic}`);
    console.log(`- 类型: ${element.elementType}`);
  });

  console.log('\n✅ 实际数据转换测试完成!');
}).catch(error => {
  console.error('\n❌ 实际数据转换测试失败:', error.message);
});
