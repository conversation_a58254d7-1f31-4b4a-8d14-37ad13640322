# 图片识别后端API设计文档

## API概述

提供图片识别服务，将上传的图片通过TextIn API进行识别，然后转换为APP端可用的JSON格式数据。

## 接口规范

### POST /api/recognize

**功能**: 上传图片并进行识别，返回结构化的元素数据

**请求参数**:
- `image`: 图片文件 (multipart/form-data)
- `imageWidth`: 图片宽度(px) - 可选，如果不提供会从图片文件中获取
- `imageHeight`: 图片高度(px) - 可选，如果不提供会从图片文件中获取

**响应格式**:
```json
{
  "success": true,
  "data": {
    "imageInfo": {
      "width": 1080,
      "height": 1920,
      "format": "jpeg"
    },
    "elements": [
      {
        "elementType": "1",
        "x": 100,
        "y": 200,
        "width": 300,
        "height": 50,
        "content": "示例文本",
        "charWidth": 25.5,
        "bold": false,
        "italic": false
      },
      {
        "elementType": "2",
        "x": 50,
        "y": 300,
        "width": 400,
        "height": 80,
        "content": "1234567890",
        "barcodeType": "CODE_128"
      }
    ]
  },
  "message": "识别成功"
}
```

## 元素类型定义

### elementType 枚举值
- `"1"`: 文本 (TEXT)
- `"2"`: 一维码/条形码 (BAR_CODE)  
- `"7"`: 二维码 (QR_CODE)
- `"10"`: 表格 (TABLE)

## 各元素类型的输出字段

### 1. 文本元素 (elementType: "1")
```json
{
  "elementType": "1",
  "x": 100,           // X坐标(px，相对图片尺寸)
  "y": 200,           // Y坐标(px，相对图片尺寸)  
  "width": 300,       // 宽度(px)
  "height": 50,       // 高度(px)
  "content": "文本内容",
  "charWidth": 25.5,  // 单个字符平均宽度(px)，APP端用于计算字体大小
  "bold": false,      // 是否加粗
  "italic": false,    // 是否斜体
  "angle": 0          // 旋转角度(度)
}
```

### 2. 一维码元素 (elementType: "2")
```json
{
  "elementType": "2",
  "x": 50,
  "y": 300,
  "width": 400,
  "height": 80,
  "content": "1234567890",    // 条码实际内容
  "barcodeType": "CODE_128", // 条码类型
  "angle": 0
}
```

### 3. 二维码元素 (elementType: "7")
```json
{
  "elementType": "7", 
  "x": 100,
  "y": 400,
  "width": 150,
  "height": 150,
  "content": "https://example.com", // 二维码实际内容
  "angle": 0
}
```

### 4. 表格元素 (elementType: "10")
```json
{
  "elementType": "10",
  "x": 50,
  "y": 500,
  "width": 500,
  "height": 200,
  "rows": 3,
  "cols": 4,
  "cells": [
    {
      "row": 0,
      "col": 0,
      "content": "单元格内容",
      "charWidth": 20.0,
      "bold": false,
      "italic": false
    }
  ]
}
```

## 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "RECOGNITION_FAILED",
    "message": "图片识别失败: 具体错误信息"
  }
}
```

## 错误码定义

- `INVALID_IMAGE`: 无效的图片文件
- `IMAGE_TOO_LARGE`: 图片文件过大
- `RECOGNITION_FAILED`: TextIn API识别失败
- `PROCESSING_ERROR`: 数据处理错误
- `INTERNAL_ERROR`: 服务器内部错误

## 技术实现要点

1. **坐标系统**: 所有坐标和尺寸都使用像素(px)单位，相对于原图片尺寸
2. **字符宽度**: 提供charWidth字段，APP端可根据此值计算合适的字体大小
3. **条码识别**: 使用@zxing/library等库解析条码内容
4. **异步处理**: 条码识别可能需要异步处理，确保响应及时返回
5. **图片格式**: 支持常见格式(JPEG, PNG, WebP等)
