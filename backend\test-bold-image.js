const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

/**
 * 测试包含粗体文本的图片识别
 */
async function testBoldImage() {
  console.log('🔍 测试包含粗体文本的图片识别...\n');

  // 测试解冻标签图片
  const imagePath = path.join(__dirname, 'data', '食品_解冻（60_40）.png');
  
  if (!fs.existsSync(imagePath)) {
    console.error('❌ 测试图片不存在:', imagePath);
    return;
  }

  console.log('📷 测试图片:', imagePath);
  
  try {
    // 调用API
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));

    const response = await axios.post('http://localhost:3001/api/recognize', formData, {
      headers: {
        ...formData.getHeaders(),
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });

    if (response.data.success) {
      const { elements } = response.data.data;
      
      console.log(`✅ 识别成功! 共识别到 ${elements.length} 个元素\n`);
      
      // 分析每个元素的粗体状态
      elements.forEach((element, index) => {
        console.log(`元素 ${index + 1}:`);
        console.log(`- 类型: ${getElementTypeName(element.elementType)}`);
        console.log(`- 内容: "${element.content}"`);
        console.log(`- 粗体: ${element.bold ? '✅ 是' : '❌ 否'}`);
        console.log(`- 斜体: ${element.italic ? '✅ 是' : '❌ 否'}`);
        
        if (element.elementType === '1') { // 文本元素
          console.log(`- 字符宽度: ${element.charWidth}px`);
        }
        
        console.log('');
      });

      // 检查是否有粗体文本
      const boldElements = elements.filter(el => el.bold);
      if (boldElements.length > 0) {
        console.log(`🎉 发现 ${boldElements.length} 个粗体元素:`);
        boldElements.forEach((element, index) => {
          console.log(`${index + 1}. "${element.content}"`);
        });
      } else {
        console.log('⚠️  没有发现粗体元素');
      }

      // 保存结果用于分析
      const outputPath = path.join(__dirname, 'debug-bold-result.json');
      fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2));
      console.log(`\n💾 结果已保存到: ${outputPath}`);

    } else {
      console.error('❌ 识别失败:', response.data.error);
    }

  } catch (error) {
    console.error('❌ API调用失败:', error.message);
  }
}

/**
 * 获取元素类型名称
 */
function getElementTypeName(elementType) {
  const typeNames = {
    '1': '文本',
    '2': '一维码',
    '7': '二维码',
    '10': '表格'
  };
  return typeNames[elementType] || `未知(${elementType})`;
}

// 执行测试
testBoldImage().then(() => {
  console.log('\n✅ 测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
