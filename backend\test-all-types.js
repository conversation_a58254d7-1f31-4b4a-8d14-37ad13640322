const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

/**
 * 测试所有类型的元素识别
 */
async function testAllTypes() {
  console.log('🔍 测试所有类型的元素识别...\n');

  const testImages = [
    { name: '解冻标签', file: '食品_解冻（60_40）.png', expected: ['文本(粗体)'] },
    { name: '肥牛卷', file: '肥牛卷（40_30）.png', expected: ['文本', '一维码'] },
    { name: '巧克力', file: '巧克力（100_70）.png', expected: ['文本(粗体)', '表格'] }
  ];

  for (const testImage of testImages) {
    console.log(`\n📷 测试图片: ${testImage.name} (${testImage.file})`);
    console.log(`期望元素类型: ${testImage.expected.join(', ')}`);
    
    const imagePath = path.join(__dirname, 'data', testImage.file);
    
    if (!fs.existsSync(imagePath)) {
      console.error(`❌ 图片不存在: ${imagePath}`);
      continue;
    }

    try {
      // 调用API
      const formData = new FormData();
      formData.append('image', fs.createReadStream(imagePath));

      const response = await axios.post('http://localhost:3001/api/recognize', formData, {
        headers: {
          ...formData.getHeaders(),
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      });

      if (response.data.success) {
        const { elements } = response.data.data;
        
        console.log(`✅ 识别成功! 共识别到 ${elements.length} 个元素`);
        
        // 统计元素类型
        const typeStats = {};
        let boldCount = 0;
        
        elements.forEach(element => {
          const typeName = getElementTypeName(element.elementType);
          typeStats[typeName] = (typeStats[typeName] || 0) + 1;
          
          if (element.bold) {
            boldCount++;
          }
        });

        console.log('📊 元素类型统计:');
        Object.entries(typeStats).forEach(([type, count]) => {
          console.log(`- ${type}: ${count}个`);
        });
        
        if (boldCount > 0) {
          console.log(`- 粗体文本: ${boldCount}个`);
        }

        // 显示详细信息
        console.log('\n📋 详细信息:');
        elements.forEach((element, index) => {
          const typeName = getElementTypeName(element.elementType);
          let info = `${index + 1}. ${typeName}`;
          
          if (element.elementType === '1') { // 文本
            info += `: "${element.content}"`;
            if (element.bold) info += ' (粗体)';
            info += ` [字符宽度: ${element.charWidth}px]`;
          } else if (element.elementType === '2') { // 一维码
            info += `: "${element.content}" (${element.barcodeType || '未知类型'})`;
          } else if (element.elementType === '10') { // 表格
            info += `: ${element.rows}行 x ${element.cols}列，${element.cells?.length || 0}个单元格`;
          } else {
            info += `: "${element.content}"`;
          }
          
          console.log(`   ${info}`);
        });

      } else {
        console.error(`❌ 识别失败: ${response.data.error}`);
      }

    } catch (error) {
      console.error(`❌ API调用失败: ${error.message}`);
    }
  }
}

/**
 * 获取元素类型名称
 */
function getElementTypeName(elementType) {
  const typeNames = {
    '1': '文本',
    '2': '一维码',
    '7': '二维码',
    '10': '表格'
  };
  return typeNames[elementType] || `未知(${elementType})`;
}

// 执行测试
testAllTypes().then(() => {
  console.log('\n✅ 所有测试完成!');
}).catch(error => {
  console.error('\n❌ 测试失败:', error.message);
});
