/**
 * API响应调试工具
 * 用于分析TextIn API响应和数据转换过程
 */

/**
 * 分析TextIn API响应结构
 */
export function analyzeTextInResponse(response) {
  console.log('=== TextIn API 响应分析 ===');
  
  if (!response) {
    console.error('响应为空');
    return null;
  }

  console.log('响应基本信息:', {
    code: response.code,
    message: response.message,
    version: response.version,
    duration: response.duration
  });

  // 检查result字段
  if (!response.result) {
    console.error('响应中缺少result字段');
    return null;
  }

  const result = response.result;
  console.log('Result结构:', {
    hasPages: !!result.pages,
    pagesCount: result.pages?.length || 0,
    hasMarkdown: !!result.markdown,
    hasStructured: !!result.structured
  });

  // 分析页面信息
  if (result.pages && result.pages.length > 0) {
    const page = result.pages[0];
    console.log('第一页信息:', {
      width: page.width,
      height: page.height,
      hasContent: !!page.content,
      contentCount: page.content?.length || 0,
      hasStructured: !!page.structured,
      structuredCount: page.structured?.length || 0
    });

    // 分析内容类型
    if (page.content) {
      const contentTypes = {};
      page.content.forEach(item => {
        const type = item.type || 'unknown';
        contentTypes[type] = (contentTypes[type] || 0) + 1;
      });
      console.log('内容类型统计:', contentTypes);

      // 显示前几个内容项的详细信息
      console.log('前3个内容项详情:');
      page.content.slice(0, 3).forEach((item, index) => {
        console.log(`内容项 ${index + 1}:`, {
          type: item.type,
          text: item.text?.substring(0, 50) + (item.text?.length > 50 ? '...' : ''),
          hasPos: !!item.pos,
          hasCharPos: !!item.char_pos,
          charPosCount: item.char_pos?.length || 0
        });
      });
    }
  }

  return result;
}

/**
 * 检查数据转换过程
 */
export function debugDataConversion(apiResponse, canvasSettings) {
  console.log('=== 数据转换调试 ===');
  
  console.log('转换参数:', canvasSettings);
  
  try {
    // 检查输入数据
    if (!apiResponse || !apiResponse.result) {
      console.error('API响应数据无效');
      return null;
    }

    const result = apiResponse.result;
    const pages = result.pages;
    
    if (!pages || pages.length === 0) {
      console.error('没有页面数据');
      return null;
    }

    const page = pages[0];
    console.log('页面尺寸:', {
      width: page.width,
      height: page.height,
      aspectRatio: page.height / page.width
    });

    // 检查画布设置
    if (!canvasSettings.canvasWidthMm || canvasSettings.canvasWidthMm <= 0) {
      console.error('画布宽度设置无效:', canvasSettings.canvasWidthMm);
      return null;
    }

    // 计算画布高度
    const aspectRatio = page.height / page.width;
    const calculatedHeight = canvasSettings.canvasWidthMm * aspectRatio;
    console.log('计算的画布高度:', calculatedHeight);

    // 检查内容数据
    const content = page.content || [];
    console.log('内容项数量:', content.length);

    if (content.length === 0) {
      console.warn('页面没有识别到任何内容');
      return null;
    }

    // 分析可转换的内容
    const convertibleContent = content.filter(item => 
      item.type && (item.type === 'paragraph' || item.type === 'table' || item.type === 'image')
    );
    
    console.log('可转换内容统计:', {
      total: content.length,
      convertible: convertibleContent.length,
      paragraphs: content.filter(item => item.type === 'paragraph').length,
      tables: content.filter(item => item.type === 'table').length,
      images: content.filter(item => item.type === 'image').length
    });

    return {
      isValid: true,
      pageInfo: {
        width: page.width,
        height: page.height,
        aspectRatio
      },
      canvasInfo: {
        widthMm: canvasSettings.canvasWidthMm,
        heightMm: calculatedHeight
      },
      contentInfo: {
        total: content.length,
        convertible: convertibleContent.length
      }
    };

  } catch (error) {
    console.error('数据转换检查失败:', error);
    return null;
  }
}

/**
 * 模拟数据转换过程
 */
export function simulateDataConversion(apiResponse, canvasSettings) {
  console.log('=== 模拟数据转换 ===');
  
  const debugInfo = debugDataConversion(apiResponse, canvasSettings);
  if (!debugInfo || !debugInfo.isValid) {
    console.error('数据转换前置检查失败');
    return null;
  }

  try {
    // 这里我们不实际转换，只是检查转换过程会遇到的问题
    const result = apiResponse.result;
    const page = result.pages[0];
    const content = page.content || [];

    console.log('开始模拟转换...');

    // 检查坐标转换器设置
    const imageWidthPx = page.width;
    const imageHeightPx = page.height;
    const canvasWidthMm = canvasSettings.canvasWidthMm;
    const canvasHeightMm = debugInfo.canvasInfo.heightMm;

    console.log('坐标转换参数:', {
      imageWidthPx,
      imageHeightPx,
      canvasWidthMm,
      canvasHeightMm,
      scaleX: canvasWidthMm / imageWidthPx,
      scaleY: canvasHeightMm / imageHeightPx
    });

    // 检查每个内容项
    const conversionResults = [];
    content.forEach((item, index) => {
      const itemResult = {
        index,
        type: item.type,
        hasPos: !!item.pos,
        hasText: !!item.text,
        hasCharPos: !!item.char_pos,
        canConvert: false,
        issues: []
      };

      // 检查是否可以转换
      if (!item.type) {
        itemResult.issues.push('缺少type字段');
      }

      if (!item.pos || !Array.isArray(item.pos) || item.pos.length !== 8) {
        itemResult.issues.push('pos字段无效');
      }

      if (item.type === 'paragraph' && !item.text) {
        itemResult.issues.push('段落缺少text字段');
      }

      itemResult.canConvert = itemResult.issues.length === 0;
      conversionResults.push(itemResult);
    });

    const convertibleCount = conversionResults.filter(r => r.canConvert).length;
    console.log('转换结果预览:', {
      total: conversionResults.length,
      convertible: convertibleCount,
      conversionRate: `${((convertibleCount / conversionResults.length) * 100).toFixed(1)}%`
    });

    // 显示有问题的项目
    const problematicItems = conversionResults.filter(r => !r.canConvert);
    if (problematicItems.length > 0) {
      console.log('有问题的内容项:');
      problematicItems.forEach(item => {
        console.log(`项目 ${item.index} (${item.type}):`, item.issues);
      });
    }

    return {
      success: convertibleCount > 0,
      totalItems: conversionResults.length,
      convertibleItems: convertibleCount,
      problematicItems: problematicItems.length,
      results: conversionResults
    };

  } catch (error) {
    console.error('模拟转换失败:', error);
    return null;
  }
}

// 在浏览器环境中暴露调试函数
if (typeof window !== 'undefined') {
  window.apiDebugger = {
    analyzeTextInResponse,
    debugDataConversion,
    simulateDataConversion
  };
  
  console.log('API调试工具已加载，可以在控制台中调用:');
  console.log('- window.apiDebugger.analyzeTextInResponse(response) - 分析API响应');
  console.log('- window.apiDebugger.debugDataConversion(response, settings) - 调试数据转换');
  console.log('- window.apiDebugger.simulateDataConversion(response, settings) - 模拟转换过程');
}
